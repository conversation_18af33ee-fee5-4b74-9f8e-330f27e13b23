1. #### ARES-24468 新增额外费用用途字段

   1. #####  录入”额外费用用途“（单据为草拟/已驳回）

   - 本章节用来描述用户在特价申请流程新建/编辑过程中，录入和编辑“额外费用用途”的页面及其规则

   1. ######  涉及页面：Step3 价格总览- 侧弹窗-其他费用 - 子步骤1：填写费用（创建/编辑状态）

      - **位置**：Step3 价格总览 -侧弹窗-其他费用——>子步骤1：填写费用

      ![img](https://myliugong.feishu.cn/space/api/box/stream/download/asynccode/?code=MWI4OWY4YzJmNDEwZTBmMjkzYWI5MzZlZWEzNjVmYWFfVjBjRWRnV0RuNEQ5Q0trVktMOWt3aWliMTJGblhicHNfVG9rZW46T3ZpNGJnSWRLb3A1ZkN4TU9wMGNWOGtmbjVkXzE3NTQzMTE2OTY6MTc1NDMxNTI5Nl9WNA)

   - **功能描述**
     - **初始化设置**：第一次进入页面，因【额外费用/台】默认为空，所以【额外费用用途】字段展示为”--“
     - **布局**：在【额外费用/台】列右侧，新增一列，列标题为【额外费用用途】，此列固定展示
     - **输入描述**：
       - 基于物料维度进行维护
       - 为支持多行文本输入的组件，总字符长度≤100，其中汉字字符数≤50
       - 可拖拉调整大小，当输入内容超出单元格可见高度时，该输入区域应出现垂直滚动条
     - **交互逻辑**：
       - 当某行物料的任一币种维度的【额外费用/台】数值＞0时，该行【额外费用用途】字段展示输入框，且必填，暗文输入提示：请输入用途
       - 当某行物料的所有币种维度【额外费用/台】数值=0或为空，该行【额外费用用途】字段输入功能禁用，展示为“--”。
     - **校验逻辑**：
       - **前置校验**：展示必填标识*，输入时，如果输入内容超出“总长度限制”或“汉字数量限制”，超出内容不能录入
       - **后置提交校验**：通过侧边栏或保存并下一步时，若不满足必填规则，则限制提交，并将问题输入框置为红色边框，同时输入框下方显示错误提示语：请输入用途。

   1. #####  查看”额外费用用途“

   - 本章节描述用户在不同阶段和页面查看已录入的“额外费用用途”时的展示规则

   1. ######  创建/编辑过程中的查看（单据状态为草拟/已驳回）

   1.  涉及页面：Step3 侧弹窗：其他费用 - 子步骤3：费用汇总

        **页面位置**：额外费用列

      ![img](https://myliugong.feishu.cn/space/api/box/stream/download/asynccode/?code=MzNmMGM3ZGNlOGQ0MTg4YzJmY2NjNmE0NjRhOGIyODFfV1FGYnRnUHZSZnowbm1qRlV0WVVUMElBWVdzalZjbFFfVG9rZW46QkNiUmJVeXBlb25Wd2t4TEhaUWNhWFRKbm5lXzE3NTQzMTE2OTY6MTc1NDMxNTI5Nl9WNA)

        **功能描述**：在各物料行各自币种【额外费用/台】金额右侧，如已填写【额外费用用途】，即该物料行展示信息图标 i，鼠标悬浮展示所有信息，格式为：`含额外费用，用途：{描述文本}`

   1.  涉及页面：Step3 价格总览 ：场景：包含以下费用项

      ![img](https://myliugong.feishu.cn/space/api/box/stream/download/asynccode/?code=NjcxZjgxZmRlOGU4ZjFmM2JhNDM3NGU1NjZlNDJjY2JfRWx4enFxWkRDZ245RnBhT0JwVktzYURVMkcwN2RBMGJfVG9rZW46TjNBeWJnSFFQb2tEd3J4cnNEcGM5eWU3bm5lXzE3NTQzMTE2OTY6MTc1NDMxNTI5Nl9WNA)

        **功能描述**：在各物料行【预估其他费用/台】单元格所有币种的其他费用金额右侧，如已填写【额外费用用途】，则展示信息图标 i，鼠标悬浮展示所有信息，格式为：`含额外费用，用途：{描述文本}`

   1. ######  查看详情页的展示

   1.  涉及页面：费用明细Tab

        **页面位置**：在各物料行的所有币种的【其他费用/列】金额右侧，如已填写【额外费用用途】，则展示信息图标 i，鼠标悬浮展示所有信息，格式为：`含额外费用，用途：{描述文本}`

      ![img](https://myliugong.feishu.cn/space/api/box/stream/download/asynccode/?code=NjkyYTdhNWU2YTc2ZmE1OTE4NDNjZDI2ODU2ZWZmNDZfTWZVWnYyMDFUVEdMdVBaZ1lrUkVZaVExY3MwNEdFNm1fVG9rZW46RXBKMWJrNkpqb0ZxdXp4NkQ2SWNYblhubk1oXzE3NTQzMTE2OTY6MTc1NDMxNTI5Nl9WNA)

   1.  涉及页面：毛利分析Tab

        **前置条件**：已生成毛利分析，存在毛利分析Tab页面

        **页面位置**：在各物料行的所有币种的【其他费用/列】金额右侧，如已填写【额外费用用途】，则展示信息图标 i，鼠标悬浮展示所有信息，格式为：`含额外费用，用途：{描述文本}`

      ![img](https://myliugong.feishu.cn/space/api/box/stream/download/asynccode/?code=YjVhMWRmYTFmODdhMDllZmQwMDRkNjdjYjE4YWQ5NWNfOFhReWtCMm5ZOXJlcDNqc2p2TlFCT3dBY0p5NEh4Y25fVG9rZW46VGRtNmJKeG91b1A2WVV4bG5nUmN5elA0bkhmXzE3NTQzMTE2OTY6MTc1NDMxNTI5Nl9WNA)

   1.  涉及页面：查看单台毛利分析

        **页面位置**：在明细分析表中，【额外费用】行标题如果【额外费用用途】不为空，则行标题追加括号展示【额外费用用途】，超出部分省略号展示，鼠标悬浮展示全部内容。

      ![img](https://myliugong.feishu.cn/space/api/box/stream/download/asynccode/?code=NGFhZjU2NTczMDg1MjQwMGU2ZTk2NmZlYzI1YzY5YmRfb0JvYXJZdlZGVWxnMGlxQmFWa3BsVzlQcHFPcVFFd3lfVG9rZW46TmpEOGJGYkpPb0JtWml4RDZxS2MxVnFxbjhjXzE3NTQzMTE2OTY6MTc1NDMxNTI5Nl9WNA)