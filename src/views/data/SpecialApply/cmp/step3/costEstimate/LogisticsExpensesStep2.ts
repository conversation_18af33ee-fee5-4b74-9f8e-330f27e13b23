import { Vue, Component, Prop } from 'vue-property-decorator';
// HTTPS
import SpecialApplyBillLogisticsExpensesApi from '@/http/price/controller/specialapply/logisticsexpenses/SpecialApplyBillLogisticsExpensesApi';
import SpecialApplyBillApi from '@/http/price/controller/specialapply/bill/SpecialApplyBillApi';
// MODELS
import SpecialApplyBill from '@/model/remote/price/api/specialapply/bill/SpecialApplyBill';
import SpecialApplyLogisticsExpensesSummary from '@/model/remote/price/api/specialapply/bill/SpecialApplyLogisticsExpensesSummary';
import SpecialApplyCurrencyExchangeRate from '@/model/remote/price/api/specialapply/bill/SpecialApplyCurrencyExchangeRate';
import { GuidePriceType } from 'model/remote/price/model/po/GuidePriceType';
import { ExportType } from 'model/remote/price/model/po/subsidiarycommissionrate/ExportType';
// COMPONENTS
import ExchangeRateDialog from './ExchangeRateDialog.vue';

@Component({
  name: 'LogisticsExpensesStep2',
  components: { ExchangeRateDialog },
})
export default class LogisticsExpensesStep2 extends Vue {
  @Prop({ type: Object, default: new SpecialApplyBill() })
  baseEntity: SpecialApplyBill; // 基础数据
  // 枚举
  GuidePriceType = GuidePriceType;
  ExportType = ExportType;

  $refs: any;
  loading: boolean = false;
  tableData: SpecialApplyLogisticsExpensesSummary[] = [];

  // 汇率相关
  exchangeRateDialogVisible: boolean = false;
  exchangeRateData: SpecialApplyCurrencyExchangeRate = new SpecialApplyCurrencyExchangeRate();
  created() {
    this.getExpensesSummary();
    this.getExchangeRateData();
  }

  // 获取费用明细
  getExpensesSummary() {
    this.loading = true;
    SpecialApplyBillLogisticsExpensesApi.getExpensesSummary(this.baseEntity.id!)
      .then((res) => {
        if (res.data) {
          this.tableData = res.data.summaryList || [];
        }
      })
      .catch((rej) => {
        this.$message.error(rej.message);
      })
      .finally(() => {
        this.loading = false;
      });
  }

  // 获取汇率数据
  getExchangeRateData() {
    SpecialApplyBillApi.listCurrencyExchangeRate(this.baseEntity.id!)
      .then((res) => {
        if (res.data) {
          this.exchangeRateData = res.data;
        }
      })
      .catch((error) => {
        console.error('获取汇率数据失败:', error);
      });
  }

  // 显示汇率弹窗
  showExchangeRateDialog() {
    this.exchangeRateDialogVisible = true;
  }

  // 获取所有币种，用于显示
  get allCurrencies() {
    const currencies = new Set<string>();

    // 从物流费用汇总数据中获取币种
    // this.tableData.forEach((row) => {
    //   if (row.currencyIds) {
    //     row.currencyIds.forEach((currency) => {
    //       if (currency.currencyId) {
    //         currencies.add(currency.currencyId);
    //       }
    //     });
    //   }
    // });

    // 从汇率数据中获取币种
    this.exchangeRateData.targetCurrencyIds.forEach((currency) => {
      if (currency.currencyId) {
        currencies.add(currency.currencyId);
      }
    });

    return Array.from(currencies).join('、');
  }
  // 获取国内段运杂费（按币种分组）
  getDomesticFeesByCurrency(
    row: SpecialApplyLogisticsExpensesSummary
  ): { fees: any[]; tip: string; feesMap: Map<string, { amount: number; symbol: string }> } {
    const currencyIds = row.currencyIds;
    // 累计币种费用
    const feesMap = new Map<string, { amount: number; symbol: string }>();
    currencyIds.forEach((currency) => {
      feesMap.set(currency.currencyId!, { amount: 0, symbol: currency.currencySymbol || '' });
    });
    // 规则缺失的费用项
    let missingItems: string[] = [];

    // 收集所有费用项的费用
    const feeItems = [
      { item: row.domesticTransportation, name: '国内运费' },
      { item: row.packing, name: '装箱费' },
      { item: row.packagingProtection, name: '包装防护费' },
    ];

    // 自营场景添加港杂费
    if (this.baseEntity.exportTypeId === 'self') {
      feeItems.push({ item: row.port, name: '港杂费' });
    }

    feeItems.forEach(({ item, name }) => {
      if (item?.ruleEmptyType) {
        // 明确标识的缺失类型："装运方案缺失"、"规则缺失"
        missingItems.push(name);
        return;
      }
      if (!item || !item.fees || item.fees.length === 0) {
        // 没有数据或费用为空，认为是规则缺失
        missingItems.push(name);
        return;
      }
      // 有费用数据，按币种累加
      item.fees.forEach((fee) => {
        if (fee.currencyId && fee.value !== null && fee.value !== undefined) {
          const existing = feesMap.get(fee.currencyId) || {
            amount: 0,
            symbol: fee.currencySymbol || '',
          };
          existing.amount += fee.value;
          feesMap.set(fee.currencyId, existing);
        }
      });
    });

    // 生成提示信息
    const tip = missingItems.length > 0 ? missingItems.join('、') + '规则缺失' : '';

    // 转换为数组供模板使用
    const feesArray = Array.from(feesMap.entries()).map(([currencyId, fee]) => ({
      currencyId,
      ...fee,
    }));

    return { fees: feesArray, tip, feesMap };
  }

  // 获取国际段运杂费（按币种分组）
  getInternationalFeesByCurrency(
    row: SpecialApplyLogisticsExpensesSummary
  ): { fees: any[]; tip: string; feesMap: Map<string, { amount: number; symbol: string }> } {
    const currencyIds = row.currencyIds;
    // 累计币种费用
    const feesMap = new Map<string, { amount: number; symbol: string }>();
    currencyIds.forEach((currency) => {
      feesMap.set(currency.currencyId!, { amount: 0, symbol: currency.currencySymbol || '' });
    });
    // 规则缺失的费用项
    let tip = '';

    const item = row.internationalTransportation;

    if (item?.ruleEmptyType) {
      // 明确标识的缺失类型："装运方案缺失"、"规则缺失"
      if (item.ruleEmptyType === '装运方案缺失') {
        tip = '未能找到装运方案，请联系物流部门补充';
      } else if (item.ruleEmptyType === '规则缺失') {
        tip = '未能找到国际运费规则，请联系物流部门补充';
      } else {
        tip = '未能找到国际运费规则，请联系物流部门补充';
      }
    } else if (!item || !item.fees || item.fees.length === 0) {
      // 没有数据或费用为空，认为是规则缺失
      tip = '未能找到国际运费规则，请联系物流部门补充';
    } else {
      // 有费用数据，按币种设置
      item.fees.forEach((fee) => {
        if (fee.currencyId && fee.value !== null && fee.value !== undefined) {
          const existing = feesMap.get(fee.currencyId) || {
            amount: 0,
            symbol: fee.currencySymbol || '',
          };
          existing.amount += fee.value;
          feesMap.set(fee.currencyId, existing);
        }
      });
    }

    // 转换为数组供模板使用
    const feesArray = Array.from(feesMap.entries()).map(([currencyId, fee]) => ({
      currencyId,
      ...fee,
    }));

    return { fees: feesArray, tip, feesMap };
  }

  // 费用合计
  getTotalAmount(row: SpecialApplyLogisticsExpensesSummary): { fees: any[]; tip: string } {
    // 非自营-买断没有国际段运杂费，直接返回国内段运杂费
    if (
      !(
        this.baseEntity.exportTypeId === ExportType.self &&
        this.baseEntity.saleMode === GuidePriceType.buyOut
      )
    ) {
      return this.getDomesticFeesByCurrency(row);
    }

    const domesticResult = this.getDomesticFeesByCurrency(row);
    const internationalResult = this.getInternationalFeesByCurrency(row);
    const currencyIds = row.currencyIds;

    // 累计币种费用
    const feesMap = new Map<string, { amount: number; symbol: string }>();
    currencyIds.forEach((currency) => {
      feesMap.set(currency.currencyId!, { amount: 0, symbol: currency.currencySymbol || '' });
    });

    // 处理规则缺失的费用项提示信息
    const tips: string[] = [];
    if (domesticResult.tip && domesticResult.tip.trim()) {
      tips.push(domesticResult.tip.trim());
    }
    if (internationalResult.tip && internationalResult.tip.trim()) {
      tips.push(internationalResult.tip.trim());
    }
    const tip = tips.join('，');

    // 相加求和
    feesMap.forEach((fee, currencyId) => {
      const domesticFee = domesticResult.feesMap.get(currencyId);
      const internationalFee = internationalResult.feesMap.get(currencyId);
      fee.amount = (domesticFee?.amount || 0) + (internationalFee?.amount || 0);
    });

    return {
      fees: Array.from(feesMap.entries()).map(([currencyId, fee]) => ({
        currencyId,
        ...fee,
      })),
      tip,
    };
  }

  // 保存数据
  async doSave(): Promise<void> {
    // 费用明细是只读的，不需要保存操作
    return Promise.resolve();
  }

  // 验证数据
  async doValidate(): Promise<boolean> {
    // 费用明细是只读的，不需要验证
    return true;
  }
}
