<template>
  <div class="tab-step1" ref="step1" :style="{ '--height': height }">
    <!-- 币种信息显示 -->
    <div class="currency-info">
      <div class="currency-left">
        <span class="label">币种：</span>
        <span class="value">{{ currency }}</span>
        <el-button type="text" @click="showExchangeRateDialog" class="exchange-rate-btn">
          查看汇率
        </el-button>
      </div>
      <div class="currency-right">
        <!-- 供货场景：国内运输保险费是否投保 -->
        <div v-if="baseEntity.exportTypeId === 'supply'" class="domestic-insurance">
          <span class="label">国内运输保险费是否投保：</span>
          <el-switch
            v-model="domesticInsurancePremium"
            size="mini"
            @change="domesticInsuranceChange"
          ></el-switch>
        </div>
        <!-- CIF场景：修改国际运输保险费率 -->
        <div v-if="baseEntity.incotermsId === 'CIF'">
          <el-button type="text" @click="showInsuranceRateDialog" class="insurance-rate-btn">
            修改国际运输保险费率
          </el-button>
        </div>
      </div>
    </div>

    <div class="tab-step1-table">
      <el-form ref="tableForm" :model="{ entity }">
        <el-table :data="entity">
          <el-table-column type="index" label="行号" width="50" fixed="left"> </el-table-column>
          <el-table-column label="整机物料" prop="matCd" minWidth="204" fixed="left">
            <template #default="scoped">
              <div class="table-cell">
                <div>物料号: {{ scoped.row.matLine.matCd }}</div>
                <div>机型: {{ scoped.row.matLine.prodMdlCode }}</div>
                <div v-ellipsis>国际产品线: {{ scoped.row.matLine.i18ProdGroupName }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="配置说明（营销）" prop="matDesc" minWidth="176">
            <template #default="scope">
              <el-tooltip
                :disabled="
                  ObjectUtil.isNullOrBlank(scope.row.matLine) ||
                    ObjectUtil.isNullOrBlank(scope.row.matLine.matDesc)
                "
                popper-class="popper-class"
                class="item"
                effect="dark"
                :content="ObjectUtil.replaceStr(scope.row.matLine.matDesc, '@!', '/')"
                placement="top-start"
              >
                <div class="ellipsis-three-line">
                  {{ ObjectUtil.replaceStr(scope.row.matLine.matDesc, '@!', '/') | text }}
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="配置说明（营销）_英文" prop="matDescEn" minWidth="224">
            <template #default="scope">
              <el-tooltip
                :disabled="
                  ObjectUtil.isNullOrBlank(scope.row.matLine) ||
                    ObjectUtil.isNullOrBlank(scope.row.matLine.matDescEn)
                "
                popper-class="popper-class"
                class="item"
                effect="dark"
                :content="ObjectUtil.replaceStr(scope.row.matLine.matDescEn, '@!', '/')"
                placement="top-start"
              >
                <div class="ellipsis-three-line">
                  {{ ObjectUtil.replaceStr(scope.row.matLine.matDescEn, '@!', '/') | text }}
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="台量" prop="qty" minWidth="50">
            <template #default="scoped">
              {{ scoped.row.matLine.limitQty ? scoped.row.matLine.qty : '不限' }}
            </template>
          </el-table-column>
          <el-table-column label="延保费/台" minWidth="176">
            <template #default="scoped">
              <!-- 未选择延保政策 -->
              <span class="cell-text" @click="doAddExtended(scoped.row, 'view', scoped.$index)">
                选择延保政策
              </span>
            </template>
          </el-table-column>
          <el-table-column label="客户佣金/台" minWidth="104">
            <template #default="scoped">
              <div
                v-for="(item, index) in scoped.row.currencyIds"
                :key="index"
                style="margin-bottom: 5px"
              >
                <number-input
                  style="width: 80px"
                  v-model="scoped.row.commission.fees[index].value"
                  size="mini"
                  xType="num"
                  :disabled="baseEntity.saleMode === 'buyOut'"
                >
                  <template slot="prepend">
                    <div style="height: 26px; line-height: 26px">
                      {{ item.currencySymbol }}
                    </div>
                  </template>
                </number-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="赠送配件金额/台" minWidth="126">
            <template #default="scoped">
              <div
                v-for="(item, index) in scoped.row.currencyIds"
                :key="index"
                style="margin-bottom: 5px"
              >
                <number-input
                  style="width: 80px"
                  v-model="scoped.row.giftAccessoryAmount.fees[index].value"
                  size="mini"
                  xType="num"
                  :disabled="baseEntity.saleMode === 'buyOut'"
                >
                  <template slot="prepend">
                    <div style="height: 26px; line-height: 26px">
                      {{ item.currencySymbol }}
                    </div>
                  </template>
                </number-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="额外费用/台" minWidth="104">
            <template #default="scoped">
              <div
                v-for="(item, index) in scoped.row.currencyIds"
                :key="index"
                style="margin-bottom: 5px"
              >
                <number-input
                  style="width: 80px"
                  v-model="scoped.row.otherExpenses.fees[index].value"
                  size="mini"
                  xType="num"
                  @change="handleExtraFeeChange(scoped.row)"
                >
                  <template slot="prepend">
                    <div style="height: 26px; line-height: 26px">
                      {{ item.currencySymbol }}
                    </div>
                  </template>
                </number-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column minWidth="164" align="center">
            <template #header>
              <span>额外费用用途</span>
            </template>
            <template #default="scoped">
              <el-form-item
                v-if="needExtraFeeUsage(scoped.row)"
                :prop="`entity.${scoped.$index}.extraFeeUsage`"
                :rules="extraFeeUsageRules"
                class="table-form-item"
              >
                <el-input
                  type="textarea"
                  :rows="4"
                  v-model="scoped.row.extraFeeUsage"
                  size="mini"
                  :maxlength="100"
                  resize="none"
                  placeholder="请输入用途"
                />
              </el-form-item>
              <div v-else>
                --
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </div>

    <!-- 汇率弹窗组件 -->
    <ExchangeRateDialog
      :visible.sync="exchangeRateDialogVisible"
      :exchange-rate-data="exchangeRateData"
      @close="exchangeRateDialogVisible = false"
    />
  </div>
</template>

<script src="./OtherExpensesStep1.ts"></script>

<style lang="scss" scoped>
.tab-step1 {
  min-height: 282px;
  height: var(--height);
  overflow-y: auto;

  .currency-info {
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .currency-left {
      display: flex;
      align-items: center;

      .label {
        margin-right: 8px;
      }

      .value {
        margin-right: 8px;
      }

      .exchange-rate-btn {
        padding: 0;
        font-size: 14px;
        color: #409eff;

        &:hover {
          color: #66b1ff;
        }
      }
    }

    .currency-right {
      .domestic-insurance {
        display: flex;
        align-items: center;

        .label {
          margin-right: 8px;
          font-size: 14px;
          color: #606266;
        }
      }

      .insurance-rate-btn {
        padding: 0;
        font-size: 14px;
        color: #409eff;

        &:hover {
          color: #66b1ff;
        }
      }
    }
  }

  &-table {
    .cell-text {
      color: #3b71fc;
      font-size: 12px;
      cursor: pointer;
    }
    .cell-btn {
      color: #79879e;
      font-size: 12px;
      margin-left: 4px;
      cursor: pointer;
    }
  }
}
/deep/ .el-table__header-wrapper .el-table__cell {
  // 以下为UI样式
  padding-top: 6px;
  padding-bottom: 8px;
}
/deep/ .el-table__fixed-header-wrapper .el-table__cell {
  // 以下为UI样式
  padding-top: 6px;
  padding-bottom: 8px;
}
/deep/ .el-table__fixed-body-wrapper .el-table__cell {
  // 以下为UI样式
  padding-top: 0px;
  padding-bottom: 0px;
}
/deep/ .el-table__body-wrapper .el-table__cell {
  // 以下为UI样式
  padding-top: 0px;
  padding-bottom: 0px;
}

// 表格内表单项样式
/deep/ .table-form-item {
  margin-bottom: 0;

  .el-form-item__content {
    line-height: normal;
  }

  .el-form-item__error {
    position: static;
  }
}
</style>
