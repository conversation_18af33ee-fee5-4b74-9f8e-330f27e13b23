<template>
  <div class="tab-step1">
    <el-form :model="entity" :rules="rules" ref="form" size="mini">
      <el-form-item required label="币种" prop="supplementPriceCcyId" label-width="80px">
        <search-remote
          width="200px"
          v-model="entity.supplementPriceCcyId"
          placeholder="输入搜索或选择"
          valueKey="dictValue"
          :queryMethod="queryCurrencyList"
          :initMethod="queryCurrencyInit"
          :valueFormat="currencyValueFormat"
          :labelFormat="currencyLabelFormat"
          :multiple="false"
          :lazyLoad="true"
          @change="currencyIdChange"
        ></search-remote>
      </el-form-item>
      <div class="tab-step1-main">
        <el-table ref="table" :data="entity.lines">
          <el-table-column label="整机物料" minWidth="180" fixed="left">
            <template #default="scoped">
              <div>物料号：{{ scoped.row.specialApplyMat.matCd }}</div>
              <div>国际产品线：{{ scoped.row.specialApplyMat.i18ProdGroupName }}</div>
              <div>机型： {{ scoped.row.specialApplyMat.prodMdlCode }}</div>
            </template>
          </el-table-column>
          <el-table-column label="配置说明（营销）" minWidth="176">
            <template #default="scope">
              <el-tooltip
                :disabled="
                  ObjectUtil.isNullOrBlank(scope.row.specialApplyMat) ||
                    ObjectUtil.isNullOrBlank(scope.row.specialApplyMat.matDesc)
                "
                popper-class="popper-class"
                class="item"
                effect="dark"
                :content="ObjectUtil.replaceStr(scope.row.specialApplyMat.matDesc, '@!', '/')"
                placement="top-start"
              >
                <div class="ellipsis-three-line">
                  {{ ObjectUtil.replaceStr(scope.row.specialApplyMat.matDesc, '@!', '/') | text }}
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="配置说明（营销）_英文" minWidth="224">
            <template #default="scope">
              <el-tooltip
                :disabled="
                  ObjectUtil.isNullOrBlank(scope.row.specialApplyMat) ||
                    ObjectUtil.isNullOrBlank(scope.row.specialApplyMat.matDescEn)
                "
                popper-class="popper-class"
                class="item"
                effect="dark"
                :content="ObjectUtil.replaceStr(scope.row.specialApplyMat.matDescEn, '@!', '/')"
                placement="top-start"
              >
                <div class="ellipsis-three-line">
                  {{ ObjectUtil.replaceStr(scope.row.specialApplyMat.matDescEn, '@!', '/') | text }}
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="台量" prop="qty" minWidth="50">
            <template #default="scoped">
              {{ scoped.row.specialApplyMat.limitQty ? scoped.row.specialApplyMat.qty : '不限' }}
            </template>
          </el-table-column>
          <el-table-column label="单台费用" align="center">
            <el-table-column minWidth="120" class-name="table-form-item">
              <template #header>
                <span style="color: #f56c6c;"> * </span>
                <span>终端价格</span>
              </template>
              <template #default="scoped">
                <el-form-item
                  v-if="entity.supplementPriceCcyId"
                  :prop="'lines.' + scoped.$index + '.terminalPrice'"
                  :rules="{
                    required: true,
                    message: '请输入',
                    trigger: ['blur', 'change'],
                  }"
                >
                  <number-input
                    v-model="scoped.row.terminalPrice"
                    size="mini"
                    xType="num"
                    :float="true"
                    :min="1"
                    :max="*********"
                    @change="triggerCurrencyValidation"
                  >
                    <template slot="prepend">
                      <div style="height: 26px; line-height: 26px">
                        {{ entity.supplementPriceCcySymbol }}
                      </div>
                    </template>
                  </number-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column minWidth="146" class-name="table-form-item">
              <template #header>
                <span style="color: #f56c6c;"> * </span>
                <span>经销商采购成本</span>
              </template>
              <template #default="scoped">
                <el-form-item
                  v-if="entity.supplementPriceCcyId"
                  :prop="'lines.' + scoped.$index + '.dlrProcurementCost'"
                  :rules="{
                    required: true,
                    message: '请输入',
                    trigger: ['blur', 'change'],
                  }"
                >
                  <number-input
                    v-model="scoped.row.dlrProcurementCost"
                    size="mini"
                    xType="num"
                    :float="true"
                    :min="1"
                    :max="*********"
                    @change="triggerCurrencyValidation"
                  >
                    <template slot="prepend">
                      <div style="height: 26px; line-height: 26px">
                        {{ entity.supplementPriceCcySymbol }}
                      </div>
                    </template>
                  </number-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column minWidth="120" label="内陆运费">
              <template #default="scoped">
                <number-input
                  v-if="entity.supplementPriceCcyId"
                  v-model="scoped.row.inlandFreight"
                  size="mini"
                  xType="num"
                  :float="true"
                  :min="0"
                  :max="*********"
                  @change="triggerCurrencyValidation"
                >
                  <template slot="prepend">
                    <div style="height: 26px; line-height: 26px">
                      {{ entity.supplementPriceCcySymbol }}
                    </div>
                  </template>
                </number-input>
              </template>
            </el-table-column>
            <el-table-column minWidth="120" label="清关费">
              <template #default="scoped">
                <number-input
                  v-if="entity.supplementPriceCcyId"
                  v-model="scoped.row.customsClearanceFee"
                  size="mini"
                  xType="num"
                  :float="true"
                  :min="0"
                  :max="*********"
                  @change="triggerCurrencyValidation"
                >
                  <template slot="prepend">
                    <div style="height: 26px; line-height: 26px">
                      {{ entity.supplementPriceCcySymbol }}
                    </div>
                  </template>
                </number-input>
              </template>
            </el-table-column>
            <el-table-column minWidth="120" label="组装费">
              <template #default="scoped">
                <number-input
                  v-if="entity.supplementPriceCcyId"
                  v-model="scoped.row.assemblyFee"
                  size="mini"
                  xType="num"
                  :float="true"
                  :min="0"
                  :max="*********"
                  @change="triggerCurrencyValidation"
                >
                  <template slot="prepend">
                    <div style="height: 26px; line-height: 26px">
                      {{ entity.supplementPriceCcySymbol }}
                    </div>
                  </template>
                </number-input>
              </template>
            </el-table-column>
            <el-table-column minWidth="120" label="税费">
              <template #default="scoped">
                <number-input
                  v-if="entity.supplementPriceCcyId"
                  v-model="scoped.row.tax"
                  size="mini"
                  xType="num"
                  :float="true"
                  :min="0"
                  :max="*********"
                  @change="triggerCurrencyValidation"
                >
                  <template slot="prepend">
                    <div style="height: 26px; line-height: 26px">
                      {{ entity.supplementPriceCcySymbol }}
                    </div>
                  </template>
                </number-input>
              </template>
            </el-table-column>
            <el-table-column minWidth="120" label="港杂/口岸费">
              <template #default="scoped">
                <number-input
                  v-if="entity.supplementPriceCcyId"
                  v-model="scoped.row.portCharge"
                  size="mini"
                  xType="num"
                  :float="true"
                  :min="0"
                  :max="*********"
                  @change="triggerCurrencyValidation"
                >
                  <template slot="prepend">
                    <div style="height: 26px; line-height: 26px">
                      {{ entity.supplementPriceCcySymbol }}
                    </div>
                  </template>
                </number-input>
              </template>
            </el-table-column>
            <el-table-column minWidth="120" label="PDI费用">
              <template #default="scoped">
                <number-input
                  v-if="entity.supplementPriceCcyId"
                  v-model="scoped.row.pdiFee"
                  size="mini"
                  xType="num"
                  :float="true"
                  :min="0"
                  :max="*********"
                  @change="triggerCurrencyValidation"
                >
                  <template slot="prepend">
                    <div style="height: 26px; line-height: 26px">
                      {{ entity.supplementPriceCcySymbol }}
                    </div>
                  </template>
                </number-input>
              </template>
            </el-table-column>
            <el-table-column minWidth="120" label="客户佣金">
              <template #default="scoped">
                <number-input
                  v-if="entity.supplementPriceCcyId"
                  v-model="scoped.row.commissionFee"
                  size="mini"
                  xType="num"
                  :float="true"
                  :min="0"
                  :max="*********"
                  @change="triggerCurrencyValidation"
                >
                  <template slot="prepend">
                    <div style="height: 26px; line-height: 26px">
                      {{ entity.supplementPriceCcySymbol }}
                    </div>
                  </template>
                </number-input>
              </template>
            </el-table-column>
            <el-table-column minWidth="120" label="融资费用">
              <template #default="scoped">
                <number-input
                  v-if="entity.supplementPriceCcyId"
                  v-model="scoped.row.financingFee"
                  size="mini"
                  xType="num"
                  :float="true"
                  :min="0"
                  :max="*********"
                  @change="triggerCurrencyValidation"
                >
                  <template slot="prepend">
                    <div style="height: 26px; line-height: 26px">
                      {{ entity.supplementPriceCcySymbol }}
                    </div>
                  </template>
                </number-input>
              </template>
            </el-table-column>
            <el-table-column minWidth="130" label="其他费用" class-name="table-form-item">
              <template #default="scoped">
                <number-input
                  v-if="entity.supplementPriceCcyId"
                  v-model="scoped.row.otherFee"
                  size="mini"
                  xType="num"
                  :float="true"
                  :min="0"
                  :max="*********"
                  @change="otherFeeChange(scoped.$index)"
                >
                  <template slot="prepend">
                    <div style="height: 26px; line-height: 26px">
                      {{ entity.supplementPriceCcySymbol }}
                    </div>
                  </template>
                </number-input>
                <el-form-item
                  v-if="entity.supplementPriceCcyId"
                  :prop="'lines.' + scoped.$index + '.otherFeeName'"
                  :rules="{
                    required: false,
                    validator: (rule, value, callback) =>
                      validatorOtherFeeName(scoped.row, value, callback),
                  }"
                >
                  <el-input
                    size="mini"
                    v-model="scoped.row.otherFeeName"
                    maxlength="20"
                    placeholder="请输入费用项名称"
                  ></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <!-- FOB场景：国际运费 -->
            <el-table-column v-if="isFOB" minWidth="120" label="国际运费">
              <template #header>
                <div style="text-align: center;">
                  <span>国际运费</span>
                  <el-tooltip content="此列币种和该物料特价申请币种一致" placement="top-start">
                    <i class="el-icon-info icon-info"></i>
                  </el-tooltip>
                </div>
              </template>
              <template #default="scoped">
                <div v-if="scoped.row.currencyIds && scoped.row.currencyIds.length > 0">
                  <div
                    v-for="currency in scoped.row.currencyIds"
                    :key="currency.currencyId"
                    style="margin-bottom: 8px"
                  >
                    <number-input
                      :value="getInternationalTransportationValue(scoped.row, currency.currencyId)"
                      size="mini"
                      xType="num"
                      :float="true"
                      :min="0"
                      :max="*********"
                      @input="
                        updateInternationalTransportation(scoped.row, currency.currencyId, $event)
                      "
                    >
                      <template slot="prepend">
                        <div style="height: 26px; line-height: 26px">
                          {{ currency.currencySymbol }}
                        </div>
                      </template>
                    </number-input>
                  </div>
                </div>
              </template>
            </el-table-column>
            <!-- FOB场景：国际运输保险费 -->
            <el-table-column v-if="isFOB" minWidth="150" label="国际运输保险费">
              <template #header>
                <div style="text-align: center;">
                  <span>国际运输保险费</span>
                  <el-tooltip content="此列币种和该物料特价申请币种一致" placement="top-start">
                    <i class="el-icon-info icon-info"></i>
                  </el-tooltip>
                </div>
              </template>
              <template #default="scoped">
                <div v-if="scoped.row.currencyIds && scoped.row.currencyIds.length > 0">
                  <div
                    v-for="currency in scoped.row.currencyIds"
                    :key="currency.currencyId"
                    style="margin-bottom: 8px"
                  >
                    <number-input
                      :value="
                        getInternationalTransportationPremiumValue(scoped.row, currency.currencyId)
                      "
                      size="mini"
                      xType="num"
                      :float="true"
                      :min="0"
                      :max="*********"
                      @input="
                        updateInternationalTransportationPremium(
                          scoped.row,
                          currency.currencyId,
                          $event
                        )
                      "
                    >
                      <template slot="prepend">
                        <div style="height: 26px; line-height: 26px">
                          {{ currency.currencySymbol }}
                        </div>
                      </template>
                    </number-input>
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </div>
    </el-form>
  </div>
</template>

<script src="./TabStep2.ts"></script>

<style lang="scss" scoped>
.tab-step1 {
  &-main {
    .table-form-item {
      .el-form-item {
        margin: 14px 0px !important;
      }
    }
  }
}
</style>
