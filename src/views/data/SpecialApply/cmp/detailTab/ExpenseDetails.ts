import { Vue, Component, Prop } from 'vue-property-decorator';
import PrecisionUtil from '@/utils/Precision';
import ObjectUtil from '@/utils/ObjectUtil';
// HTTPS
import SpecialApplyBillApi from '@/http/price/controller/specialapply/bill/SpecialApplyBillApi';
// MODELS
import SpecialApplyBill from '@/model/remote/price/api/specialapply/bill/SpecialApplyBill';
import SpecialApplyMatLineScopeExwPriceOverview from '@/model/remote/price/api/specialapply/mat/SpecialApplyMatLineScopeExwPriceOverview';
import SpecialApplyFeeDetail from '@/model/remote/price/api/specialapply/priceoverview/SpecialApplyFeeDetail';
import SpecialApplyFeeDetailLine from '@/model/remote/price/api/specialapply/priceoverview/SpecialApplyFeeDetailLine';
// COMPONENTES
@Component({
  name: 'ExpenseDetails',
  components: {},
})
export default class TabStep1 extends Vue {
  @Prop({ type: Object, default: new SpecialApplyBill() })
  baseEntity: SpecialApplyBill; // 基础数据
  $refs: any;
  tableData: any[] = [];
  tableSpan: any = {};
  PrecisionUtil = PrecisionUtil;
  ObjectUtil = ObjectUtil;
  feeDetailEntity: SpecialApplyFeeDetail = new SpecialApplyFeeDetail(); // 费用明细数据
  // 【出口类型=自营】且【销售模式=买断】的场景，价格信息tab展示
  get showPriceInfo() {
    if (this.baseEntity.saleMode === 'buyOut' && this.baseEntity.exportTypeId === 'self') {
      return true;
    }
    return false;
  }
  created() {
    this.getData();
  }
  getData() {
    // 获取费用明细
    SpecialApplyBillApi.getFeeDetails(this.baseEntity.id!)
      .then((res) => {
        this.feeDetailEntity = res.data || new SpecialApplyFeeDetail();
        this.getTableData(this.feeDetailEntity);
      })
      .catch((rej) => {
        this.$message.error(rej.message);
      });
  }
  getTableData(result: SpecialApplyFeeDetail) {
    let arr: any = [];
    let spanArr: any = {}; // 表格数据合并
    result.lines.forEach((item: SpecialApplyFeeDetailLine, index: number) => {
      if (index === 0) {
        spanArr[index] = item.specialApplyMat?.exwPriceList.length || 0;
      } else {
        let lastNum = 0;
        for (let key in spanArr) {
          lastNum = Number(key) + spanArr[key];
        }
        spanArr[lastNum] = item.specialApplyMat?.exwPriceList.length || 0;
      }
      if (item.specialApplyMat?.exwPriceList && item.specialApplyMat.exwPriceList.length > 0) {
        item.specialApplyMat.exwPriceList.forEach(
          (priceItem: SpecialApplyMatLineScopeExwPriceOverview) => {
            arr.push({
              ...item,
              ...item.specialApplyMat,
              ...priceItem,
              id: priceItem.id,
              specialApplyMat: item.specialApplyMat,
              applicationScope: priceItem.applicationScope,
              // 初始化默认值：EXW设备特价 = 设备指导价，调价差值 = 空
              specialPrice: Number(priceItem.specialPrice || priceItem.exwPrice),
              differAmt: priceItem.differAmt ? Number(priceItem.differAmt) : null,
              adjustRange:
                priceItem.adjustRange !== null && priceItem.adjustRange !== undefined
                  ? Number(
                      PrecisionUtil.toFixed(PrecisionUtil.floatMul(priceItem.adjustRange, 100), 2)
                    )
                  : null,
              mergeIndex: index,
            });
          }
        );
      }
    });
    this.tableData = arr;
    this.tableSpan = spanArr;
  }
  // 表格合并
  spanMethod({ row, column, rowIndex, columnIndex }) {
    // 对前3列进行合并：整机物料号、物料描述（营销）、物料描述（营销）_英文
    if (columnIndex <= 2) {
      if (this.tableSpan[rowIndex]) {
        return {
          rowspan: this.tableSpan[rowIndex],
          colspan: 1,
        };
      } else {
        return {
          rowspan: 0,
          colspan: 0,
        };
      }
    }
  }
  // 计算调价幅度百分比
  calculateAdjustRange(adjustRange) {
    if ((!adjustRange && adjustRange !== 0) || adjustRange < 0) {
      return null;
    }
    return Number(PrecisionUtil.toFixed(adjustRange * 100, 2));
  }

  // 格式化调价幅度显示
  formatAdjustRange(row) {
    // 调价幅度为0时显示0不显示--
    if (row.adjustRange === 0) {
      return '0';
    }
    if (row.adjustRange !== null && row.adjustRange !== undefined) {
      return `-${row.adjustRange}%`;
    }
    return '--';
  }

  formatSpecialPriceApplicableArea(row) {
    if (row && row.applicationScope) {
      let applicationScope = row.applicationScope;
      let content = `子公司：${applicationScope.sbsdyName}`;

      if (this.baseEntity.specialType != 'adjustZoneGuidePrice') {
        content += `\n办事处：${applicationScope.ofcName}`;
      }

      content += `\n销往国：${this.formatCtryName(applicationScope)}`;

      if (this.baseEntity.specialType == 'importantProjects') {
        content += `\n客户：${applicationScope.custName}`;
      }

      content += `\n币种：${applicationScope.currencyId}`;

      return content;
    }
    return '--';
  }

  // 格式化销往国
  formatCtryName(applicationScope) {
    if (!applicationScope.ctrys || applicationScope.ctrys.length <= 0) {
      return '--';
    }
    let res: string[] = [];
    for (let ctry of applicationScope.ctrys) {
      for (let applicationScopeCtryItem of ctry.applicationScopeCtry) {
        res.push(applicationScopeCtryItem.name as string);
      }
    }
    if (res.length <= 0) {
      return '--';
    }
    return res.join('、');
  }

  // 获取国际运费值
  getInternationalTransportationValue(row: any, currencyId: string) {
    console.dir(row);
    if (!row.internationalTransportations) return null;
    const item = row.internationalTransportations.find(
      (item: any) => item.currencyId === currencyId
    );
    return item ? item.value : null;
  }

  // 获取国际运输保险费值
  getInternationalTransportationPremiumValue(row: any, currencyId: string) {
    if (!row.internationalTransportationPremiums) return null;
    const item = row.internationalTransportationPremiums.find(
      (item: any) => item.currencyId === currencyId
    );
    return item ? item.value : null;
  }

  // 判断该行是否有额外费用用途
  hasExtraFeeUsage(row: any): boolean {
    return !!row.extraFeeUsage;
  }

  // 获取额外费用用途
  getExtraFeeUsage(row: any): string {
    return row.extraFeeUsage || '';
  }
}
