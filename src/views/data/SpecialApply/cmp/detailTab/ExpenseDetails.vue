<template>
  <div class="tab-step1">
    <div class="tab-step1-main">
      <el-table ref="table" :data="tableData" :span-method="spanMethod" border>
        <el-table-column label="整机物料号" minWidth="180" fixed="left">
          <template #default="scoped">
            <div>物料号：{{ scoped.row.specialApplyMat.matCd }}</div>
            <div>国际产品线：{{ scoped.row.specialApplyMat.i18ProdGroupName }}</div>
            <div>机型： {{ scoped.row.specialApplyMat.prodMdlCode }}</div>
          </template>
        </el-table-column>
        <el-table-column label="配置说明（营销）" minWidth="176">
          <template #default="scope">
            <el-tooltip
              :disabled="
                ObjectUtil.isNullOrBlank(scope.row.specialApplyMat) ||
                  ObjectUtil.isNullOrBlank(scope.row.specialApplyMat.matDesc)
              "
              popper-class="popper-class"
              class="item"
              effect="dark"
              :content="ObjectUtil.replaceStr(scope.row.specialApplyMat.matDesc, '@!', '/')"
              placement="top-start"
            >
              <div class="ellipsis-three-line">
                {{ ObjectUtil.replaceStr(scope.row.specialApplyMat.matDesc, '@!', '/') | text }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="配置说明（营销）_英文" minWidth="224">
          <template #default="scope">
            <el-tooltip
              :disabled="
                ObjectUtil.isNullOrBlank(scope.row.specialApplyMat) ||
                  ObjectUtil.isNullOrBlank(scope.row.specialApplyMat.matDescEn)
              "
              popper-class="popper-class"
              class="item"
              effect="dark"
              :content="ObjectUtil.replaceStr(scope.row.specialApplyMat.matDescEn, '@!', '/')"
              placement="top-start"
            >
              <div class="ellipsis-three-line">
                {{ ObjectUtil.replaceStr(scope.row.specialApplyMat.matDescEn, '@!', '/') | text }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="特价适用范围" minWidth="180">
          <template #default="scope">
            <el-tooltip
              :disabled="ObjectUtil.isNullOrBlank(scope.row.applicationScope)"
              popper-class="popper-class"
              class="item"
              effect="dark"
              placement="top-start"
            >
              <!-- 换行显示 -->
              <div
                slot="content"
                style="white-space: pre-line"
                v-html="formatSpecialPriceApplicableArea(scope.row)"
              ></div>
              <div class="table-cell">
                <div>子公司： {{ scope.row.applicationScope.sbsdyName }}</div>
                <div v-if="baseEntity.specialType != 'adjustZoneGuidePrice'">
                  办事处：{{ scope.row.applicationScope.ofcName }}
                </div>
                <div class="text-ellipsis">
                  销往国：{{ formatCtryName(scope.row.applicationScope) }}
                </div>
                <div v-if="baseEntity.specialType == 'importantProjects'">
                  客户：{{ scope.row.applicationScope.custName }}
                </div>
                <div>币种：{{ scope.row.applicationScope.currencyId }}</div>
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="台量" minWidth="134">
          <template #default="scoped">
            <div class="table-cell" style="display: flex;">
              {{ scoped.row.limitQty ? scoped.row.qty : '不限' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="设备指导价/台" prop="exwPrice" minWidth="120">
          <template #default="scoped">
            <div v-if="scoped.row.exwPrice > 0" style="text-align: right" class="table-cell-price">
              <div style="display: inline-block">
                {{ scoped.row.applicationScope.currencySymbol }}
              </div>
              <div style="display: inline-block">
                {{ scoped.row.exwPrice | numberFilter('--', 0, true) }}
              </div>
            </div>
            <div v-else style="text-align: right" class="table-cell-price">
              <div style="display: inline-block">
                {{ scoped.row.applicationScope.currencySymbol }}
              </div>
              <div style="display: inline-block">0</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column minWidth="134">
          <template #header>
            <span>调价幅度</span>
            <el-tooltip
              content="系统自动计算，调价幅度=调价差值/EXW设备指导价%"
              placement="top-start"
            >
              <i class="el-icon-info icon-info"></i>
            </el-tooltip>
          </template>
          <template #default="scoped">
            <div style="text-align: right; color: #606266;" class="table-cell-price">
              {{ formatAdjustRange(scoped.row) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column minWidth="160">
          <template #header>
            <span>EXW设备特价/台</span>
            <el-tooltip
              content="仅支持输入正整数，EXW设备特价=EXW设备指导价-调价差值"
              placement="top-start"
            >
              <i class="el-icon-info icon-info"></i>
            </el-tooltip>
          </template>
          <template #default="scoped">
            <div style="text-align: right" class="table-cell-price">
              <div style="display: inline-block">
                {{ scoped.row.applicationScope.currencySymbol }}
              </div>
              <div style="display: inline-block">
                {{ scoped.row.specialPrice | numberFilter('--', 0, true) }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column minWidth="190">
          <template #header>
            <span>价格有效期</span>
            <el-tooltip
              content="申请特价的期望有效期，特价的实际有效期以审批通过后的价格有效期为准"
              placement="top-start"
            >
              <i class="el-icon-info icon-info"></i>
            </el-tooltip>
          </template>
          <template #default="scoped">
            <div style="text-align: left" v-if="scoped.row.beginDate || scoped.row.endDate">
              {{ scoped.row.beginDate }}～{{ scoped.row.endDate }}
            </div>
            <div style="text-align: left" v-else>
              --
            </div>
          </template>
        </el-table-column>
        <!-- 包含物流费用及其他费用 -->
        <el-table-column v-if="baseEntity.otherExpenses" label="预估费用" align="center">
          <el-table-column minWidth="120" label="预估物流费用/台">
            <template #header>
              <div style="text-align: center;">
                <span>预估物流费用/台</span>
                <el-tooltip content="预估费用中计算的物流费用合计" placement="top-start">
                  <i class="el-icon-info icon-info"></i>
                </el-tooltip>
              </div>
            </template>
            <template #default="scoped">
              <div class="table-cell" style="display: flex;">
                <div>
                  {{ scoped.row.applicationScope.currencySymbol }}
                </div>
                <div>
                  {{ scoped.row.logisticsExpenses | numberFilter('--', 0, false) }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column minWidth="120" label="预估其他费用/台">
            <template #header>
              <div style="text-align: center;">
                <span>预估其他费用/台</span>
                <el-tooltip content="预估费用中计算的其他费用合计" placement="top-start">
                  <i class="el-icon-info icon-info"></i>
                </el-tooltip>
              </div>
            </template>
            <template #default="scoped">
              <div class="table-cell" style="display: flex; align-items: center;">
                <div style="display: flex;">
                  <div>
                    {{ scoped.row.applicationScope.currencySymbol }}
                  </div>
                  <div>
                    {{ scoped.row.otherExpenses | numberFilter('--', 0, false) }}
                  </div>
                </div>
                <!-- 额外费用用途信息图标 -->
                <el-tooltip
                  v-if="hasExtraFeeUsage(scoped.row)"
                  :content="`含额外费用，用途：${getExtraFeeUsage(scoped.row)}`"
                  placement="top"
                  effect="dark"
                >
                  <i class="el-icon-info icon-info" />
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
          <el-table-column minWidth="120" label="预估综合特价/台">
            <template #header>
              <div style="text-align: center;">
                <span>预估综合特价/台</span>
                <el-tooltip
                  content="限制输入正整数，EXW设备特价/台=预估综合特价/台-预估物流费用/台-预估其他费用/台"
                  placement="top-start"
                >
                  <i class="el-icon-info icon-info"></i>
                </el-tooltip>
              </div>
            </template>
            <template #default="scoped">
              <div class="table-cell" style="display: flex;">
                <div>
                  {{ scoped.row.applicationScope.currencySymbol }}
                </div>
                <div>
                  {{ scoped.row.total | numberFilter('--', 0, false) }}
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table-column>
        <!-- 【出口类型=自营】且【销售模式=买断】时，展示终端价格的费用项 -->
        <el-table-column v-if="showPriceInfo" label="单台终端价格费用" align="center">
          <el-table-column minWidth="120" label="终端价格">
            <template #default="scoped">
              <div
                v-if="feeDetailEntity.supplementPriceCcyId"
                class="table-cell"
                style="display: flex;"
              >
                <div>
                  {{ feeDetailEntity.supplementPriceCcySymbol }}
                </div>
                <div>{{ scoped.row.terminalPrice | numberFilter('--', 0, false) }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column minWidth="130" label="经销商采购成本">
            <template #default="scoped">
              <div class="table-cell" style="display: flex;">
                <div>
                  {{ feeDetailEntity.supplementPriceCcySymbol }}
                </div>
                <div>
                  {{ scoped.row.dlrProcurementCost | numberFilter('--', 0, false) }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column minWidth="120" label="内陆运费">
            <template #default="scoped">
              <div class="table-cell" style="display: flex;">
                <div>
                  {{ feeDetailEntity.supplementPriceCcySymbol }}
                </div>
                <div>{{ scoped.row.inlandFreight | numberFilter('--', 0, false) }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column minWidth="120" label="清关费">
            <template #default="scoped">
              <div class="table-cell" style="display: flex;">
                <div>
                  {{ feeDetailEntity.supplementPriceCcySymbol }}
                </div>
                <div>
                  {{ scoped.row.customsClearanceFee | numberFilter('--', 0, false) }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column minWidth="120" label="组装费">
            <template #default="scoped">
              <div class="table-cell" style="display: flex;">
                <div>
                  {{ feeDetailEntity.supplementPriceCcySymbol }}
                </div>
                <div>{{ scoped.row.assemblyFee | numberFilter('--', 0, false) }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column minWidth="120" label="税费">
            <template #default="scoped">
              <div class="table-cell" style="display: flex;">
                <div>
                  {{ feeDetailEntity.supplementPriceCcySymbol }}
                </div>
                <div>{{ scoped.row.tax | numberFilter('--', 0, false) }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column minWidth="120" label="港杂/口岸费">
            <template #default="scoped">
              <div class="table-cell" style="display: flex;">
                <div>
                  {{ feeDetailEntity.supplementPriceCcySymbol }}
                </div>
                <div>{{ scoped.row.portCharge | numberFilter('--', 0, false) }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column minWidth="120" label="PDI费用">
            <template #default="scoped">
              <div class="table-cell" style="display: flex;">
                <div>
                  {{ feeDetailEntity.supplementPriceCcySymbol }}
                </div>
                <div>{{ scoped.row.pdiFee | numberFilter('--', 0, false) }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column minWidth="120" label="客户佣金">
            <template #default="scoped">
              <div class="table-cell" style="display: flex;">
                <div>
                  {{ feeDetailEntity.supplementPriceCcySymbol }}
                </div>
                <div>{{ scoped.row.commissionFee | numberFilter('--', 0, false) }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column minWidth="120" label="融资费用">
            <template #default="scoped">
              <div class="table-cell" style="display: flex;">
                <div>
                  {{ feeDetailEntity.supplementPriceCcySymbol }}
                </div>
                <div>{{ scoped.row.financingFee | numberFilter('--', 0, false) }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column minWidth="130" label="其他费用" class-name="table-form-item">
            <template #default="scoped">
              <div class="table-cell" style="display: flex;">
                <div>
                  {{ feeDetailEntity.supplementPriceCcySymbol }}
                </div>
                <div>{{ scoped.row.otherFee | numberFilter('--', 0, false) }}</div>
              </div>
              <div v-if="scoped.row.otherFeeName">
                {{ scoped.row.otherFeeName }}
              </div>
            </template>
          </el-table-column>

          <!-- FOB场景 -->
          <el-table-column
            v-if="baseEntity.incotermsId === 'FOB'"
            minWidth="130"
            label="国际运费"
            class-name="table-form-item"
          >
            <template #header>
              <div style="text-align: center;">
                <span>国际运费</span>
                <el-tooltip content="此列币种和该物料特价申请币种一致" placement="top-start">
                  <i class="el-icon-info icon-info"></i>
                </el-tooltip>
              </div>
            </template>
            <template #default="scoped">
              <div class="table-cell" style="display: flex;">
                <div>
                  {{ scoped.row.applicationScope.currencySymbol }}
                </div>
                <div>
                  {{
                    getInternationalTransportationValue(
                      scoped.row,
                      scoped.row.applicationScope.currencyId
                    ) | numberFilter('--', 0, false)
                  }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            v-if="baseEntity.incotermsId === 'FOB'"
            minWidth="150"
            label="国际运输保险费"
            class-name="table-form-item"
          >
            <template #header>
              <div style="text-align: center;">
                <span>国际运输保险费</span>
                <el-tooltip content="此列币种和该物料特价申请币种一致" placement="top-start">
                  <i class="el-icon-info icon-info"></i>
                </el-tooltip>
              </div>
            </template>
            <template #default="scoped">
              <div class="table-cell" style="display: flex;">
                <div>
                  {{ scoped.row.applicationScope.currencySymbol }}
                </div>
                <div>
                  {{
                    getInternationalTransportationPremiumValue(
                      scoped.row,
                      scoped.row.applicationScope.currencyId
                    ) | numberFilter('--', 0, false)
                  }}
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script src="./ExpenseDetails.ts"></script>

<style lang="scss" scoped>
.tab-step1 {
  &-text {
    margin-bottom: 10px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    &-set {
      font-size: 12px;
      color: #242633;
      margin-right: 8px;
    }
  }
  &-main {
    .icon-info {
      margin-top: 4px;
      color: #a1b0c8;
      font-size: 14px;
      margin: 0px 4px;
      &:hover {
        color: $--color-primary;
      }
    }
    .table-cell-price {
      display: flex;
      font-weight: 800;
    }
  }
  /deep/ .el-dropdown-link {
    cursor: pointer;
    color: #409eff;
    font-size: 12px;
  }
  /deep/ .el-radio-group {
    .el-radio {
      margin-right: 10px;
      .el-radio__label {
        font-size: 12px;
        font-weight: normal;
        color: #606266 !important;
      }
    }
  }

  .icon-info {
    margin-top: 4px;
    color: #a1b0c8;
    font-size: 14px;
    margin: 0px 4px;
    &:hover {
      color: $--color-primary;
    }
  }
}
</style>
