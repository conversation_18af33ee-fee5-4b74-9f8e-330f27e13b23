/*
 * @Author: 张文轩
 * @Date: 2024-08-27 19:05:36
 * @LastEditTime: 2024-12-04 14:10:14
 * @LastEditors: 张文轩
 * @Description:
 * @FilePath: \ltc-web-ui\src\views\data\SpecialApply\cmp\drawer\AgainCheckDialog.ts
 * 记得注释
 */
import { Vue, Component } from 'vue-property-decorator';
import PrecisionUtil from '@/utils/Precision';
import ObjectUtil from '@/utils/ObjectUtil';
import SpecialApplyPriceOverviewApi from '@/http/price/controller/specialapply/price/SpecialApplyPriceOverviewApi';
import SpecialApplyMatLineScopeExwPriceOverview from '@/model/remote/price/api/specialapply/mat/SpecialApplyMatLineScopeExwPriceOverview';
import CalMatLineOtherFee from '@/model/remote/price/api/specialapply/priceoverview/CalMatLineOtherFee';
import SpecialApplyPriceOverviewMatLineV2 from '@/model/remote/price/api/specialapply/bill/SpecialApplyPriceOverviewMatLineV2';
import NumberInput from '@/components/form/number-input/NumberInput.vue';
import ThousNumber from '@/components/form/thous-number/ThousNumber.vue';
@Component({
  name: 'AgainCheckDialog',
  components: { NumberInput, ThousNumber },
})
export default class AgainCheckDialog extends Vue {
  $refs: any;
  title: string = '价格专员归档';
  specialType: string = '';
  otherExpenses: boolean = false; // 是否包含物流费用等其他费用
  entity: any[] = [];
  tableData: any[] = [];
  tableSpan: any = {};
  comment: string = ''; // 备注
  rules: any = {
    priceDate: [
      {
        required: true,
        message: `请选择价格有效期`,
        trigger: 'change',
      },
    ],
  };
  ObjectUtil = ObjectUtil;
  // 表格行错误标志
  tableErrorFlag: boolean[] = [];
  // 表格行错误信息
  tableErrorMsg: string[] = [];
  onConfirm: (data, comment) => void;
  onCancel: () => void;

  created() {
    this.getTableData();
  }
  getTableData() {
    let arr: any = [];
    let spanArr: any = {}; // 表格数据合并
    this.entity.forEach((item, index) => {
      if (index === 0) {
        spanArr[index] = item.exwPriceList.length;
      } else {
        let lastNum = 0;
        for (let key in spanArr) {
          lastNum = Number(key) + spanArr[key];
        }
        spanArr[lastNum] = item.exwPriceList.length;
      }
      if (item.exwPriceList && item.exwPriceList.length > 0) {
        item.exwPriceList.forEach((priceItem) => {
          // 根据otherExpenses状态决定计算方式
          let calculatedData = {};

          if (this.otherExpenses) {
            // 当otherExpenses为true时，前端无法计算，使用后端数据
            calculatedData = {
              adjustRange:
                priceItem.adjustRange !== null && priceItem.adjustRange !== undefined
                  ? Number(
                      PrecisionUtil.toFixed(PrecisionUtil.floatMul(priceItem.adjustRange, 100), 2)
                    )
                  : null,
            };
          } else {
            // 当otherExpenses为false时，若specialPrice为空，则使用指导价
            calculatedData = {
              specialPrice: priceItem.specialPrice || priceItem.exwPrice,
              adjustRange: this.calculateAdjustRange(priceItem.differAmt, priceItem.exwPrice),
            };
          }

          arr.push({
            ...item,
            ...priceItem,
            ...calculatedData,
            specialApplyMat: item,
            priceDate: [priceItem.beginDate, priceItem.endDate],
            mergeIndex: index,
          });
        });
      }
    });
    this.tableData = arr;
    this.tableSpan = spanArr;
    // 初始化错误标志
    this.tableErrorFlag = new Array(arr.length).fill(false);
    this.tableErrorMsg = new Array(arr.length).fill('');
  }
  setTableData() {
    let arr: SpecialApplyPriceOverviewMatLineV2[] = [];
    this.tableData.forEach((item) => {
      let itemIndex = arr.findIndex((i) => {
        return i.matId === item.matId;
      });
      if (itemIndex > -1) {
        // 找到对应的物料，添加到其exwPriceList中
        let priceItem = new SpecialApplyMatLineScopeExwPriceOverview();
        priceItem.id = item.id;
        priceItem.applicationScope = item.applicationScope;
        priceItem.limitQty = item.limitQty;
        priceItem.qty = item.qty;
        priceItem.exwPrice = item.exwPrice;
        priceItem.adjustRange = item.adjustRange
          ? PrecisionUtil.floatDiv(item.adjustRange, 100)
          : item.adjustRange;
        priceItem.differAmt = item.differAmt;
        priceItem.specialPrice = Number(item.specialPrice);
        priceItem.otherExpenses = item.otherExpenses;
        priceItem.total = item.total;
        priceItem.logisticsExpenses = item.logisticsExpenses;
        priceItem.total = item.total;
        priceItem.beginDate = item.priceDate[0];
        priceItem.endDate = item.priceDate[1];

        arr[itemIndex].exwPriceList.push(priceItem);
      } else {
        // 创建新的物料记录
        let matLine = new SpecialApplyPriceOverviewMatLineV2();
        matLine.matId = item.matId;
        matLine.owner = item.owner;
        matLine.line = item.line;
        matLine.matCd = item.matCd;
        matLine.i18ProdGroupId = item.i18ProdGroupId;
        matLine.i18ProdGroupName = item.i18ProdGroupName;
        matLine.prodMdlId = item.prodMdlId;
        matLine.prodMdlCode = item.prodMdlCode;
        matLine.matDesc = item.matDesc;
        matLine.matDescEn = item.matDescEn;

        // 创建价格项
        let priceItem = new SpecialApplyMatLineScopeExwPriceOverview();
        priceItem.id = item.id;
        priceItem.applicationScope = item.applicationScope;
        priceItem.limitQty = item.limitQty;
        priceItem.qty = item.qty;
        priceItem.exwPrice = item.exwPrice;
        priceItem.adjustRange = item.adjustRange
          ? PrecisionUtil.floatDiv(item.adjustRange, 100)
          : item.adjustRange;
        priceItem.differAmt = item.differAmt;
        priceItem.specialPrice = Number(item.specialPrice);
        priceItem.otherExpenses = item.otherExpenses;
        priceItem.total = item.total;
        priceItem.logisticsExpenses = item.logisticsExpenses;
        priceItem.beginDate = item.priceDate[0];
        priceItem.endDate = item.priceDate[1];

        matLine.exwPriceList = [priceItem];
        arr.push(matLine);
      }
    });
    return arr;
  }
  // 表格合并
  spanMethod({ row, column, rowIndex, columnIndex }) {
    // 对前3列进行合并：整机物料号、物料描述（营销）、物料描述（营销）_英文
    if (columnIndex <= 2) {
      if (this.tableSpan[rowIndex]) {
        return {
          rowspan: this.tableSpan[rowIndex],
          colspan: 1,
        };
      } else {
        return {
          rowspan: 0,
          colspan: 0,
        };
      }
    }
  }
  // 计算调价幅度百分比
  calculateAdjustRange(differAmt, exwPrice) {
    if ((!differAmt && differAmt !== 0) || differAmt < 0 || !exwPrice || exwPrice <= 0) {
      return null;
    }
    return Number(PrecisionUtil.toFixed((differAmt / exwPrice) * 100, 2));

    // return Number(
    //   PrecisionUtil.toFixed(
    //     PrecisionUtil.floatMul(PrecisionUtil.floatDiv(differAmt, exwPrice), 100),
    //     2
    //   )
    // );
  }

  // 格式化调价幅度显示
  formatAdjustRange(row) {
    if (row.adjustRange === 0) {
      return '0';
    }
    if (row.adjustRange !== null && row.adjustRange !== undefined) {
      return `-${row.adjustRange}%`;
    }
    return '--';
  }
  // 台量限制变更
  limitQtyChange(row, index) {
    if (!row.limitQty) {
      // row.qty = null;
    } else {
      row.qty = row.qty || 1;
    }
  }
  // 台量数量变更
  qtyChange(row, index) {
    if (row.limitQty) {
      if (!row.qty || row.qty < 1) {
        row.qty = 1;
      } else if (row.qty > 999999) {
        row.qty = 999999;
      }
    }
  }
  // 调价差值变更（核心计算逻辑）
  differAmtChange(row, index) {
    if (row.differAmt !== null && row.differAmt !== undefined && row.differAmt !== '') {
      // 确保为数字类型
      row.differAmt = Number(row.differAmt);
      // 确保为非负整数（调价差值能等于0）
      if (row.differAmt < 0) {
        row.differAmt = 0;
      }
      row.differAmt = Math.round(row.differAmt);

      // 限制最大值：确保调价差值不会使EXW设备特价小于1
      // EXW设备特价 = 设备指导价 - 调价差值，所以调价差值最大值 = 设备指导价 - 1
      const maxDifferAmt = row.exwPrice - 1;
      if (row.differAmt > maxDifferAmt) {
        row.differAmt = maxDifferAmt;
      }

      // 计算 EXW设备特价 = 设备指导价 - 调价差值
      row.specialPrice = PrecisionUtil.floatSub(row.exwPrice, row.differAmt);

      // 确保EXW设备特价不小于1（双重保险）
      if (row.specialPrice < 1) {
        row.specialPrice = 1;
        row.differAmt = PrecisionUtil.floatSub(row.exwPrice, row.specialPrice);
      }
    } else {
      // 差值为空时，特价等于指导价
      row.specialPrice = row.exwPrice;
      row.differAmt = null;
    }
    // 重新计算调价幅度
    row.adjustRange = this.calculateAdjustRange(row.differAmt, row.exwPrice);

    // 调用后端接口更新行数据
    this.calMatOtherFee(row, index);
  }
  // EXW设备特价变更
  specialPriceChange(row, index) {
    if (row.specialPrice !== null && row.specialPrice !== undefined && row.specialPrice !== '') {
      // 确保为数字类型
      row.specialPrice = Number(row.specialPrice);

      // 确保特价不超过指导价
      if (row.specialPrice > row.exwPrice) {
        row.specialPrice = row.exwPrice;
      }
      // 确保为正整数且不小于1
      if (row.specialPrice < 1) {
        row.specialPrice = 1;
      }
      row.specialPrice = Math.round(row.specialPrice);

      // 反向计算调价差值 = 设备指导价 - EXW设备特价
      row.differAmt = PrecisionUtil.floatSub(row.exwPrice, row.specialPrice);
      if (row.differAmt < 0) {
        row.differAmt = null;
      }
    } else {
      // 特价为空时，重置为指导价
      row.specialPrice = row.exwPrice;
      row.differAmt = null;
    }
    // 重新计算调价幅度
    row.adjustRange = this.calculateAdjustRange(row.differAmt, row.exwPrice);

    // 调用后端接口更新行数据
    this.calMatOtherFee(row, index);
  }

  // 预估综合特价变更处理
  totalChange(row: any, index: number) {
    if (!this.otherExpenses || !row.total) {
      return;
    }

    // 确保为数字类型
    row.total = Number(row.total);

    // 调用后端接口更新行数据
    this.calMatOtherFee(row, index);
  }

  // 调用后端接口重新计算
  async calMatOtherFee(row: any, index: number) {
    try {
      let params = new CalMatLineOtherFee();
      params.owner = row.owner;
      params.matId = row.matId;
      params.line = row.line;
      params.matCd = row.matCd;
      params.i18ProdGroupId = row.i18ProdGroupId;
      params.i18ProdGroupName = row.i18ProdGroupName;
      params.prodMdlId = row.prodMdlId;
      params.prodMdlCode = row.prodMdlCode;
      params.matDesc = row.matDesc;
      params.matDescEn = row.matDescEn;
      params.limitQty = row.limitQty;
      params.qty = row.qty;

      let exwPrice = new SpecialApplyMatLineScopeExwPriceOverview();
      exwPrice.id = row.id;
      exwPrice.applicationScope = row.applicationScope;
      exwPrice.limitQty = row.limitQty;
      exwPrice.qty = row.qty;
      exwPrice.exwPrice = row.exwPrice;
      exwPrice.adjustRange = PrecisionUtil.floatDiv(row.adjustRange || 0, 100);
      exwPrice.specialPrice = row.specialPrice;
      exwPrice.differAmt = row.differAmt;
      exwPrice.otherExpenses = row.otherExpenses;
      exwPrice.logisticsExpenses = row.logisticsExpenses;
      exwPrice.total = row.total;
      params.exwPrice = exwPrice;

      const res = await SpecialApplyPriceOverviewApi.calMatOtherFee(params);
      if (res.data) {
        let rowData = {
          ...row,
          ...res.data.exwPrice,
          // 保留原来的价格有效期，因为后端没返回
          // beginDate: row.beginDate,
          // endDate: row.endDate,
          priceDate: row.priceDate,
          adjustRange:
            res.data.exwPrice?.adjustRange !== null && res.data.exwPrice?.adjustRange !== undefined
              ? Number(
                  PrecisionUtil.toFixed(
                    PrecisionUtil.floatMul(res.data.exwPrice.adjustRange, 100),
                    2
                  )
                )
              : null,
        };
        // 重置错误标志和错误信息
        this.$set(this.tableErrorFlag, index, false);
        this.$set(this.tableErrorMsg, index, '');
        this.$set(this.tableData, index, rowData);
      }
    } catch (error) {
      console.error(error);
      this.$message.error((error as any).message);
      // 如果otherExpenses为true
      if (this.otherExpenses) {
        const newRow = JSON.parse(JSON.stringify(row));
        newRow.specialPrice = null;
        newRow.differAmt = null;
        newRow.adjustRange = null;
        // 清空预估物流费用后预估其他费用
        newRow.logisticsExpenses = null;
        newRow.otherExpenses = null;
        // 设置错误信息
        this.$set(this.tableErrorFlag, index, true);
        this.$set(this.tableErrorMsg, index, (error as any).message);
        this.$set(this.tableData, index, newRow);
      }
    }
  }

  formatSpecialPriceApplicableArea(row) {
    if (row && row.applicationScope) {
      let applicationScope = row.applicationScope;
      let content = `子公司：${applicationScope.sbsdyName}`;

      if (this.specialType != 'adjustZoneGuidePrice') {
        content += `\n办事处：${applicationScope.ofcName}`;
      }

      content += `\n销往国：${this.formatCtryName(applicationScope)}`;

      if (this.specialType == 'importantProjects') {
        content += `\n客户：${applicationScope.custName}`;
      }

      content += `\n币种：${applicationScope.currencyId}`;

      return content;
    }
    return '--';
  }
  // 格式化销往国
  formatCtryName(applicationScope) {
    if (!applicationScope.ctrys || applicationScope.ctrys.length <= 0) {
      return '--';
    }
    let res: string[] = [];
    for (let ctry of applicationScope.ctrys) {
      for (let applicationScopeCtryItem of ctry.applicationScopeCtry) {
        res.push(applicationScopeCtryItem.name as string);
      }
    }
    if (res.length <= 0) {
      return '--';
    }
    return res.join('、');
  }

  handleCancel() {
    if (this.onCancel) {
      this.onCancel();
    }
    this.$emit('hide');
  }
  doConfirm() {
    this.$refs.form.validate((valid) => {
      if (valid) {
        if (this.onConfirm) {
          this.onConfirm(this.setTableData(), this.comment);
        }
        this.$emit('hide');
      }
    });
  }
}
