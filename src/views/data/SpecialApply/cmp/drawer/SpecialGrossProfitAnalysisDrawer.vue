<template>
  <el-drawer
    ref="SpecialGrossProfitAnalysisDrawer"
    :title="grossProfitAnalysisDrawertitle"
    direction="rtl"
    size="70%"
    :visible.sync="localValue"
  >
    <div class="drawer" v-loading="loading">
      <div class="drawer-title">
        <div class="drawer-title-name">分析币种</div>
        <div class="drawer-title-value f-c-c">
          [CNY]人民币
        </div>
      </div>
      <!-- 单台且非供货时显示 -->
      <div class="drawer-sub" v-if="!isSupplyType">
        <div class="drawer-sub-left">
          <div class="drawer-sub-left-title drawer-sub-text">
            <span style="color:red;">*</span> 汇率类型
          </div>
          <div class="drawer-sub-left-desc">其他币种最终都会根据选择的汇率转换成CNY</div>
          <div class="drawer-sub-left-select">
            <el-select
              v-model="currentExchangeRate"
              size="mini"
              style="width: 100%;"
              @change="changeExchangeRate"
            >
              <el-option
                v-for="item in exchangeRateOptions"
                :key="item.type"
                :label="item.label"
                :value="item.type"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="drawer-sub-right">
          <div class="drawer-sub-right-title drawer-sub-text">汇率</div>
          <div class="drawer-sub-right-desc">{{ exchangeRate }}</div>
        </div>
      </div>
      <!-- 汇总模块 -->
      <Panel header="汇总分析">
        <div class="summary">不含国内物流费用：</div>
        <div ref="chart" style="width: 100%;height: 400px"></div>
      </Panel>
      <!-- 明细分析表格 -->
      <Panel header="明细分析">
        <el-table
          :data="detailAnalyseList"
          border
          style="width: 100%"
          :row-class-name="tableRowClassName"
        >
          <el-table-column label="费用项">
            <template #default="scope">
              <div class="table-row f-c-c">
                {{ scope.row.item }}
                <el-tooltip
                  v-if="scope.row.item.includes('额外费用') && hasExtraFeeUsage"
                  :content="extraFeeTooltipContent"
                  placement="top"
                  effect="dark"
                >
                  <div>
                    <span>（</span><span class="extra-fee-suffix">{{ extraFeeSuffix }}</span
                    ><span>）</span>
                  </div>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="申请特价">
            <template #default="scope">
              <template v-if="scope.row.isRate">
                <div class="table-row f-c-c">
                  {{ scope.row.salePriceFeeRate | RatioFilter }}
                </div>
              </template>
              <template v-else>
                <div
                  class="table-row f-c-c"
                  v-if="scope.row.salePriceCurrencySymbol === null && scope.row.salePrice === null"
                >
                  --
                </div>
                <div class="table-row f-c-c" v-else>
                  <div class="symbol">{{ scope.row.salePriceCurrencySymbol | text }}</div>
                  <div>
                    {{
                      scope.row.salePrice
                        | numberFilter('--', getCostPricePrecision(scope.row.item), true)
                    }}
                  </div>
                </div>
              </template>
            </template>
          </el-table-column>
          <el-table-column label="年度定价">
            <template #default="scope">
              <template v-if="scope.row.isRate">
                <div class="table-row f-c-c">
                  {{ scope.row.annualPriceFeeRate | RatioFilter }}
                </div>
              </template>
              <template v-else>
                <div
                  class="table-row f-c-c"
                  v-if="
                    scope.row.annualPriceCurrencySymbol === null && scope.row.annualPrice === null
                  "
                >
                  --
                </div>
                <div class="table-row f-c-c" v-else>
                  <div class="symbol">{{ scope.row.annualPriceCurrencySymbol | text }}</div>
                  <div>
                    {{
                      scope.row.annualPrice
                        | numberFilter('--', getCostPricePrecision(scope.row.item), true)
                    }}
                  </div>
                </div>
              </template>
            </template>
          </el-table-column>
        </el-table>
        <div class="separate">
          <el-table
            :data="otherDetailAnalyseList"
            border
            style="width: 100%"
            :row-class-name="tableRowClassName"
          >
            <el-table-column label="不包含国内物流费用" align="center">
              <el-table-column label="">
                <template #default="scope">
                  <div class="f-w">{{ scope.row.dimension }}</div>
                </template>
              </el-table-column>
              <el-table-column label="特价_毛利额/台">
                <template #default="scope">
                  <div
                    class="table-row f-c-c"
                    v-if="scope.row.profitSymbol === null && scope.row.profitValue === null"
                  >
                    --
                  </div>
                  <div class="table-row f-c-c" v-else>
                    <div class="symbol">{{ scope.row.profitSymbol | text }}</div>
                    <div v-if="scope.row.profitValue !== null && scope.row.profitValue < 0">
                      -
                    </div>
                    <div>{{ Math.abs(scope.row.profitValue) | numberFilter('--', 0, true) }}</div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="特价_毛利率/台">
                <template #default="scope">
                  <div class="table-row f-c-c">
                    {{ scope.row.profitRate | numberFilter('--', 2, true)
                    }}<span v-if="scope.row.profitRate !== null">%</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="年度定价_毛利额/台">
                <template #default="scope">
                  <div
                    class="table-row f-c-c"
                    v-if="
                      scope.row.annualPricingProfitSymbol === null &&
                        scope.row.annualPricingProfitValue === null
                    "
                  >
                    --
                  </div>
                  <div class="table-row f-c-c" v-else>
                    <div class="symbol">{{ scope.row.annualPricingProfitSymbol | text }}</div>
                    <div
                      v-if="
                        scope.row.annualPricingProfitValue !== null &&
                          scope.row.annualPricingProfitValue < 0
                      "
                    >
                      -
                    </div>
                    <div>
                      {{
                        Math.abs(scope.row.annualPricingProfitValue) | numberFilter('--', 0, true)
                      }}
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="年度定价_毛利率/台">
                <template #default="scope">
                  <div class="table-row f-c-c">
                    {{ scope.row.annualPricingProfitRate | numberFilter('--', 2, true)
                    }}<span v-if="scope.row.annualPricingProfitRate !== null">%</span>
                  </div>
                </template>
              </el-table-column>
            </el-table-column>
          </el-table>
        </div>
      </Panel>
    </div>
  </el-drawer>
</template>

<script src="./SpecialGrossProfitAnalysisDrawer.ts"></script>

<style lang="scss" scoped>
/deep/ .el-drawer {
  .el-drawer__header {
    height: 40px;
    line-height: 40px;
    color: #242633;
    font-size: 14px;
    padding: 0px 20px;
    margin-bottom: 16px;
  }

  .el-drawer__body {
    padding: 0 20px 20px;

    .drawer {
      &-title {
        height: 50px;
        color: #242633;
        &-name {
          font-weight: bold;
        }

        &-value {
          height: 32px;
          line-height: 18px;
        }
      }

      .summary {
        height: 20px;
        font-weight: bold;
        color: #242633;
        line-height: 20px;
      }

      &-sub {
        display: flex;
        justify-content: space-between;

        &-left {
          flex: 1;
          padding-right: 24px;

          &-desc {
            color: #79879e;
            height: 18px;
            line-height: 18px;
            margin: 4px 0;
          }
        }

        &-right {
          width: 250px;

          &-desc {
            color: #242633;
            height: 18px;
            line-height: 18px;
            margin: 7px 0;
          }
        }

        &-text {
          font-weight: bold;
          color: #242633;
          height: 18px;
          line-height: 18px;
        }
      }

      .total {
        width: 100%;
        background: #fff5e6;
        border-radius: 4px;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-around;
        padding: 8px 12px;
        margin: 12px 0;
        box-sizing: border-box;
        color: #242633;

        &-item {
          height: 50px;
          width: 30%;
          margin-bottom: 12px;

          &-name {
            font-weight: bold;
          }

          &-value {
            margin-top: 8px;
          }

          &-normal {
            height: 18px;
            line-height: 18px;
          }
        }
      }

      .f-c-c {
        display: flex;
        align-items: center;
      }

      .f-w {
        font-weight: bold;
      }

      .separate {
        margin-top: 24px;
        border: 4px solid #3b71fc;
      }

      .table-row {
        height: 32px;
      }

      .hight-light-row {
        background: #fff5e6;
        font-weight: bold;
      }

      .hide-row {
        display: none;
      }
    }
  }

  .special-text {
    color: #242633;
    font-weight: bold;
    display: flex;
  }
}

.textLength {
  white-space: normal;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4; // 代表文本超长最多显示2行，可自行调整最多显示的行数
  -webkit-box-orient: vertical;
}

.extra-fee-suffix {
  display: inline-block;
  max-width: 220px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: bottom;
}
</style>
