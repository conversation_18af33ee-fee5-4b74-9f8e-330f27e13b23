<template>
  <detail-page :showTitle="false" :showFilter="false">
    <template #title>
      <div>报价申请详情</div>
    </template>
    <template #actions>
      <detail-actions
        :entity="entity"
        :actions="actions"
        @doAction="doAction"
        @doBackList="doBackList"
      ></detail-actions>
    </template>
    <template #main>
      <div class="detail">
        <detail-header
          :entity="entity"
          :tipInfo="tipInfo"
          @showMissRule="showMissRule"
        ></detail-header>
        <div class="detail-main">
          <detail-base-info :entity="entity"></detail-base-info>
          <detail-logistics-info
            :entity="entity"
            :applyOptional="applyOptional"
          ></detail-logistics-info>
          <Panel header="申请信息">
            <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
              <el-tab-pane v-if="showApplyDescription" label="申请说明" name="applyDesc">
                <ApplyDescriptionTab :id="id" :base-entity="entity"></ApplyDescriptionTab>
              </el-tab-pane>
              <el-tab-pane v-if="showFeeDetails" label="费用明细" name="feeDetail">
                <tab-fee-detail ref="feeDetail" :entity="entity"></tab-fee-detail>
              </el-tab-pane>
              <el-tab-pane v-if="showProfitAnalysis" label="毛利分析" name="third">
                <div class="third-tab">
                  <div class="third-tab-title">
                    <el-button
                      style="font-size: 14px; font-weight: bold;"
                      type="text"
                      size="mini"
                      @click="viewGrossProfitAnalysisDetail(grossProfitAnalysisInfo, false)"
                    >
                      查看整单毛利分析
                    </el-button>
                  </div>
                  <el-table
                    v-loading="grossProfitAnalysisLoading"
                    :data="grossProfitAnalysisInfo.profitAnalysis"
                    max-height="500px"
                  >
                    <el-table-column label="整机物料" minWidth="180" fixed="left">
                      <template #default="scope">
                        <div class="table-cell">
                          <div class="table-cell-icon">
                            <i class="iconfont icon-ic_pin_incline" />
                          </div>
                          <div>物料号：{{ scope.row.matCd }}</div>
                          <div>
                            机型：{{
                              (scope.row.quotationMat && scope.row.quotationMat.prodMdlCd) | text
                            }}
                          </div>
                          <div>
                            国际产品线：{{
                              (scope.row.quotationMat && scope.row.quotationMat.i18ProdGroupName)
                                | text
                            }}
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="配置说明（营销）"
                      minWidth="200"
                      prop="matDesc"
                      show-overflow-tooltip
                    >
                      <template #default="scope">
                        <el-tooltip
                          :disabled="
                            ObjectUtil.isNullOrBlank(scope.row.quotationMat) ||
                              ObjectUtil.isNullOrBlank(scope.row.quotationMat.matDesc)
                          "
                          popper-class="popper-class"
                          class="item"
                          effect="dark"
                          :content="
                            ObjectUtil.replaceStr(scope.row.quotationMat.matDesc, '@!', '/')
                          "
                          placement="top"
                          :open-delay="1000"
                        >
                          <div class="textLength">
                            <template>
                              {{
                                ObjectUtil.replaceStr(scope.row.quotationMat.matDesc, '@!', '/')
                                  | text
                              }}
                            </template>
                          </div>
                        </el-tooltip>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="配置说明（营销）_英文"
                      minWidth="200"
                      prop="matDescEn"
                      show-overflow-tooltip
                    >
                      <template #default="scope">
                        <el-tooltip
                          :disabled="
                            ObjectUtil.isNullOrBlank(scope.row.quotationMat) ||
                              ObjectUtil.isNullOrBlank(scope.row.quotationMat.matDescEn)
                          "
                          popper-class="popper-class"
                          class="item"
                          effect="dark"
                          :content="
                            ObjectUtil.replaceStr(scope.row.quotationMat.matDescEn, '@!', '/')
                          "
                          placement="top"
                          :open-delay="1000"
                        >
                          <div class="textLength">
                            <template>
                              {{
                                ObjectUtil.replaceStr(scope.row.quotationMat.matDescEn, '@!', '/')
                                  | text
                              }}
                            </template>
                          </div>
                        </el-tooltip>
                      </template>
                    </el-table-column>
                    <el-table-column label="EXW设备指导价/台" minWidth="160" align="right">
                      <template #default="scope">
                        <span
                          style="color: #000; font-weight: 700; display: flex; justify-content: flex-end;"
                          ><span>{{ entity.currencySymbol }}</span
                          >{{
                            (scope.row.quotationMat && scope.row.quotationMat.exwPrice)
                              | numberFilter('--', 0, true)
                          }}</span
                        >
                      </template>
                    </el-table-column>
                    <el-table-column label="台量" width="50">
                      <template #default="scope">
                        <span style="color: #000; font-weight: 700; display: flex">{{
                          (scope.row.quotationMat && scope.row.quotationMat.qty)
                            | numberFilter('--', 0, true)
                        }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="发运信息" minWidth="150">
                      <template #default="scope">
                        <div>
                          发货地/出发地：{{
                            scope.row.quotationMat
                              ? scope.row.quotationMat.domesticFreightLandName
                              : '--'
                          }}
                        </div>
                        <div v-if="entity.exportTypeId !== 'supply'">
                          起运港/起运站：{{
                            scope.row.quotationMat
                              ? scope.row.quotationMat.domesticFreightPortName
                              : '--'
                          }}
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column label="延保政策" minWidth="120" align="right">
                      <template #default="scope">
                        <el-button
                          style="padding: 0px;"
                          type="text"
                          @click="doViewExtended(scope.row, 'detail')"
                        >
                          查看
                        </el-button>
                      </template>
                    </el-table-column>
                    <el-table-column label="折扣率/台" minWidth="100" align="right">
                      <template #default="scope">
                        <div
                          v-if="scope.row.quotationMat && scope.row.quotationMat.discountRate < 0"
                          style="color: rgb(255, 160, 1); font-weight: bold"
                        >
                          {{
                            PrecisionUtil.floatMul(
                              Math.abs(scope.row.quotationMat.discountRate),
                              100
                            ) | numberFilter('--', 2, true)
                          }}%
                        </div>
                        <div v-else>--</div>
                      </template>
                    </el-table-column>
                    <el-table-column label="单台费用" align="center">
                      <el-table-column label="设备报价" minWidth="100" align="right">
                        <template #default="scope">
                          <div
                            style="color: #000; font-weight: 700; display: flex; justify-content: flex-end;"
                          >
                            <div>{{ entity.currencySymbol }}</div>
                            <div>
                              {{
                                Math.ceil(scope.row.quotationMat.actualPrice)
                                  | numberFilter('--', 0, true)
                              }}
                            </div>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column label="国内运杂费" minWidth="100" align="right">
                        <template #default="scope">
                          <div
                            style="color: #000; font-weight: 700; display: flex; justify-content: flex-end;"
                          >
                            <div>{{ entity.currencySymbol }}</div>
                            <div>
                              {{
                                scope.row.quotationMat.domesticFreight | numberFilter('--', 2, true)
                              }}
                            </div>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column label="国际运杂费" minWidth="100" align="right">
                        <template #default="scope">
                          <div
                            style="color: #000; font-weight: 700; display: flex; justify-content: flex-end;"
                          >
                            <div>{{ entity.currencySymbol }}</div>
                            <div>
                              {{
                                scope.row.quotationMat.internationalFreight
                                  | numberFilter('--', 2, true)
                              }}
                            </div>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column label="其他费用" minWidth="100" align="right">
                        <template #default="scope">
                          <div
                            style="color: #000; font-weight: 700; display: flex; justify-content: flex-end; align-items: center;"
                          >
                            <div>{{ entity.currencySymbol }}</div>
                            <div>
                              {{
                                Math.ceil(scope.row.quotationMat.otherFreight)
                                  | numberFilter('--', 0, true)
                              }}
                            </div>
                            <!-- 额外费用用途信息图标 -->
                            <el-tooltip
                              v-if="hasExtraFeeUsageInProfit(scope.row)"
                              :content="`含额外费用，用途：${getExtraFeeUsageInProfit(scope.row)}`"
                              placement="top"
                              effect="dark"
                            >
                              <i class="el-icon-info icon-info" />
                            </el-tooltip>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column label="综合报价" minWidth="100" align="right">
                        <template #default="scope">
                          <div
                            style="color: #000; font-weight: 700; display: flex; justify-content: flex-end;"
                          >
                            <div>{{ entity.currencySymbol }}</div>
                            <div>
                              {{
                                Math.ceil(scope.row.quotationMat.quotationTotal)
                                  | numberFilter('--', 0, true)
                              }}
                            </div>
                          </div>
                        </template>
                      </el-table-column>
                    </el-table-column>
                    <el-table-column minWidth="100" fixed="right" align="right">
                      <template #header>
                        <span>小计</span>
                        <el-tooltip content="整机物料综合报价合计" placement="top-start">
                          <i class="el-icon-info icon-info"></i>
                        </el-tooltip>
                      </template>
                      <template #default="scope">
                        <div
                          style="color: #000; font-weight: 700; display: flex; justify-content: flex-end;"
                        >
                          <div>{{ entity.currencySymbol }}</div>
                          <div>
                            {{
                              scope.row.quotationMat.quotationTotalLineTotal
                                | numberFilter('--', 0, true)
                            }}
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column label="单台毛利分析" minWidth="100" fixed="right">
                      <template #default="scope">
                        <el-button
                          type="text"
                          @click="viewGrossProfitAnalysisDetail(scope.row, true)"
                        >
                          查看
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-tab-pane>
              <el-tab-pane label="付款方式" name="second">
                <div class="second-tab">
                  <div class="tab-step2-toolbar-text" style="display: flex">
                    <div style="margin-right: 4px">报价总金额:</div>
                    <div>{{ entity.currencySymbol }}</div>
                    <div>{{ totalAmount | numberFilter('--', 0, true) }}</div>
                  </div>
                  <el-table
                    max-height="500px"
                    ref="table"
                    :data="paymentEntity.lines"
                    v-loading="loading"
                  >
                    <el-table-column
                      label="付款方式"
                      minWidth="100"
                      prop="paymentTypeId"
                      show-overflow-tooltip
                    >
                      <template #default="scope">
                        {{ scope.row.paymentTypeNameEn + '/' + scope.row.paymentTypeName }}
                      </template>
                    </el-table-column>
                    <el-table-column label="付款比例" minWidth="104" prop="paymentRatio">
                      <template #default="scope">
                        {{ (scope.row.paymentRatio * 100) | numberFilter('--', 2, true) }}%
                      </template>
                    </el-table-column>
                    <el-table-column label="付款金额" minWidth="160">
                      <template #default="scope">
                        <div style="display: flex">
                          <div>
                            {{ entity.currencySymbol }}
                          </div>
                          <div v-if="scope.row.paymentRatio">
                            {{ getAmount(scope.$index) }}
                          </div>
                          <div v-else>--</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column label="天数" minWidth="80">
                      <template #default="scope">
                        {{ scope.row.days ? scope.row.days : '--' }}
                        <!-- <number-input
                        v-if="scope.row.payForward"
                        size="mini"
                        v-model="scope.row.days"
                        xType="num"
                        @change="daysChange(scope.row, scope.$index)"
                      >
                      </number-input> -->
                        <!-- <div v-else>--</div> -->
                      </template>
                    </el-table-column>
                    <el-table-column label="远期贴现费率（年利率）" minWidth="124">
                      <template #default="scope">
                        <!-- <div v-if="scope.row.payForward && scope.row.calForwardRate === true">
                        <div v-if="!ObjectUtil.isNullOrBlank(scope.row.forwardDiscountRate)">

                        </div>
                        <div v-else style="color: #de3232">未获取到</div>
                      </div> -->
                        <div v-if="scope.row.forwardDiscountRate">
                          {{
                            PrecisionUtil.floatMul(scope.row.forwardDiscountRate, 100)
                              | numberFilter('--', 2, true)
                          }}%
                        </div>
                        <div v-else>--</div>
                      </template>
                    </el-table-column>
                    <el-table-column label="信保费率" minWidth="104">
                      <template #default="scope">
                        <!-- <div v-if="scope.row.payForward && scope.row.calCreditRate === true">
                        <div v-if="!ObjectUtil.isNullOrBlank(scope.row.creditInsuranceRate)">

                        </div>
                        <div v-else>未获取到</div>
                      </div> -->
                        <div v-if="scope.row.creditInsuranceRate">
                          {{
                            PrecisionUtil.floatMul(scope.row.creditInsuranceRate, 100)
                              | numberFilter('--', 4, true)
                          }}%
                        </div>
                        <div v-else>--</div>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-tab-pane>
              <el-tab-pane
                v-if="entity.exportTypeId === 'self' && entity.saleMode === 'commission'"
                label="子公司佣金"
                name="sixth"
              >
                <div class="sixth-tab">
                  <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                    <div>
                      <span style="margin-right: 4px;">提佣子公司: </span>
                      <span>{{ entity.sbsdyName }}</span>
                    </div>
                    <div style="display: flex; color: #000; font-weight: 600; font-size: 14px;;">
                      <div style="margin-right: 4px;">整单子公司佣金合计:</div>
                      <div>{{ entity.currencySymbol }}</div>
                      <div>{{ entity.commissionSbsdyAmount | numberFilter('--', 0, true) }}</div>
                    </div>
                  </div>
                  <el-table
                    max-height="500px"
                    key="commissionTable"
                    ref="commissionTable"
                    :data="commissionTableData"
                    :span-method="commissionSpanMethod"
                    border
                  >
                    <el-table-column
                      label="国际产品线"
                      prop="i18ProdGroupName"
                      minWidth="92"
                      fixed="left"
                    >
                      <template #default="scope">
                        <div>{{ scope.row.i18ProdGroupName }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column label="整机物料" prop="matCd" minWidth="172" fixed="left">
                      <template #default="scope">
                        <div class="table-cell">
                          <div class="table-cell-icon">
                            <i class="iconfont icon-ic_pin_incline" />
                          </div>
                          <div>物料号:</div>
                          <div>{{ scope.row.matCd }}</div>
                          <div>机型: {{ scope.row.prodMdlCd }}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column label="配置说明（营销）" prop="matDesc" minWidth="176">
                      <template #default="scope">
                        <el-tooltip
                          :disabled="ObjectUtil.isNullOrBlank(scope.row.matDesc)"
                          popper-class="popper-class"
                          class="item"
                          effect="dark"
                          :content="ObjectUtil.replaceStr(scope.row.matDesc, '@!', '/')"
                          placement="top-start"
                          :open-delay="1000"
                        >
                          <div class="ellipsis-three-line">
                            {{ ObjectUtil.replaceStr(scope.row.matDesc, '@!', '/') | text }}
                          </div>
                        </el-tooltip>
                      </template>
                    </el-table-column>
                    <el-table-column label="配置说明（营销）_英文" prop="matDescEn" minWidth="224">
                      <template #default="scope">
                        <el-tooltip
                          :disabled="ObjectUtil.isNullOrBlank(scope.row.matDescEn)"
                          popper-class="popper-class"
                          class="item"
                          effect="dark"
                          :content="ObjectUtil.replaceStr(scope.row.matDescEn, '@!', '/')"
                          placement="top-start"
                          :open-delay="1000"
                        >
                          <div class="ellipsis-three-line">
                            {{ ObjectUtil.replaceStr(scope.row.matDescEn, '@!', '/') | text }}
                          </div>
                        </el-tooltip>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="FOB价/台"
                      prop="sbsdyCommissionAmount"
                      minWidth="112"
                      align="right"
                    >
                      <template #default="scope">
                        <div style="display: flex; justify-content: flex-end;">
                          <div>
                            {{ entity.currencySymbol }}
                          </div>
                          <div>
                            {{ scope.row.sbsdyCommissionAmount | numberFilter('--', 2, true) }}
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column label="台量" prop="qty" minWidth="50">
                      <template #default="scope">
                        <div>{{ scope.row.qty }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column label="子公司佣金率" prop="sbsdyCommissionRate" minWidth="110">
                      <template #default="scope">
                        <div v-if="!ObjectUtil.isNullOrBlank(scope.row.sbsdyCommissionRate)">
                          {{
                            PrecisionUtil.floatMul(scope.row.sbsdyCommissionRate, 100)
                              | numberFilter('--', 2, false)
                          }}%
                        </div>
                        <div v-else>--</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="子公司佣金/台"
                      prop="subsidiariesCommissionFee"
                      minWidth="112"
                      align="right"
                      fixed="right"
                    >
                      <template #default="scope">
                        <div style="display: flex; justify-content: flex-end;">
                          <div>
                            {{ entity.currencySymbol }}
                          </div>
                          <div>
                            {{ scope.row.subsidiariesCommissionFee | numberFilter('--', 0, true) }}
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column minWidth="150" align="right" fixed="right">
                      <template #header>
                        <span>子公司佣金小计</span>
                        <el-tooltip
                          content="相同国际产品线所有物料的子公司佣金合计"
                          placement="top-start"
                        >
                          <i class="el-icon-info icon-info"></i>
                        </el-tooltip>
                      </template>
                      <template #default="scope">
                        <div style="display: flex; justify-content: flex-end;">
                          <div>
                            {{ entity.currencySymbol }}
                          </div>
                          <div>
                            {{ scope.row.sbsdyCommissionAmountTotal | numberFilter('--', 0, true) }}
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-tab-pane>
              <el-tab-pane v-if="uploadNormalFile || uploadPrivacyFile" label="附件" name="fifth">
                <div class="fifth-tab">
                  <el-tabs v-model="activeNameEx">
                    <el-tab-pane v-if="uploadNormalFile" label="普通附件" name="normal">
                      <el-upload action="" :before-upload="onUpload1" :show-file-list="false">
                        <el-button size="mini" style="margin-bottom: 8px">上传附件</el-button>
                      </el-upload>
                      <el-table v-loading="loading1" :data="fileList1" max-height="500px">
                        <el-table-column label="附件名称" minWidth="240">
                          <template #default="scope">
                            <div
                              v-if="showPreview(scope.row)"
                              style="color: #3b71fc"
                              @click="previewFile(scope.row)"
                            >
                              {{ scope.row.fileName }}
                            </div>
                            <div v-else>
                              {{ scope.row.fileName }}
                            </div>
                          </template>
                        </el-table-column>
                        <el-table-column label="类型" minWidth="120">
                          <template #default>
                            <div>普通附件</div>
                          </template>
                        </el-table-column>
                        <el-table-column label="上传信息" minWidth="240">
                          <template #default="scope">
                            {{ scope.row.createUserName + '/' + scope.row.createTime }}
                          </template>
                        </el-table-column>
                        <el-table-column label="操作" width="128">
                          <template #default="scope">
                            <el-button type="text" @click="onDownload(scope.row)">下载</el-button>
                            <el-button type="text" @click="onDelete(scope.row)">删除</el-button>
                          </template>
                        </el-table-column>
                      </el-table>
                    </el-tab-pane>
                    <el-tab-pane v-if="uploadPrivacyFile" label="保密附件" name="privacy">
                      <el-upload action="" :before-upload="onUpload2" :show-file-list="false">
                        <el-button size="mini" style="margin-bottom: 8px">上传附件</el-button>
                      </el-upload>
                      <el-table v-loading="loading2" :data="fileList2" max-height="500px">
                        <el-table-column label="附件名称" minWidth="240">
                          <template #default="scope">
                            <div
                              v-if="showPreview(scope.row)"
                              style="color: #3b71fc"
                              @click="previewFile(scope.row)"
                            >
                              {{ scope.row.fileName }}
                            </div>
                            <div v-else>
                              {{ scope.row.fileName }}
                            </div>
                          </template>
                        </el-table-column>
                        <el-table-column label="类型" minWidth="120">
                          <template #default>
                            <div>保密附件</div>
                          </template>
                        </el-table-column>
                        <el-table-column label="上传信息" minWidth="240">
                          <template #default="scope">
                            {{ scope.row.createUserName + '/' + scope.row.createTime }}
                          </template>
                        </el-table-column>
                        <el-table-column label="操作" width="128">
                          <template #default="scope">
                            <el-button type="text" @click="onDownload(scope.row)">下载</el-button>
                            <el-button type="text" @click="onDelete(scope.row)">删除</el-button>
                          </template>
                        </el-table-column>
                      </el-table>
                    </el-tab-pane>
                  </el-tabs>
                </div>
              </el-tab-pane>
            </el-tabs>
          </Panel>
        </div>
      </div>
      <DiscountDrawer v-model="showDiscountDrawer" :baseEntity="entity" />
      <ApproveShippingDiscountsDrawer
        v-model="showApproveShippingDiscountsDrawer"
        :actions="actions"
        :baseEntity="entity"
        @refresh="initEntity"
      >
      </ApproveShippingDiscountsDrawer>
      <InquiryDrawer
        v-model="showInquiryDrawer"
        :baseEntity="entity"
        :action="action"
        @refresh="closeInquiryDrawer"
      >
      </InquiryDrawer>
      <GrossProfitAnalysisDrawer
        v-model="showGrossProfitAnalysisDrawer"
        :baseEntity="entity"
        :data="grossProfitAnalysisDrawerInfo"
        :exchangeRateOptions="exchangeRateTypeList"
        :isSingle="isSingle"
      >
      </GrossProfitAnalysisDrawer>
      <ProtectedDrawer
        v-model="showProtectedDrawer"
        :baseEntity="entity"
        :action="action"
        @refresh="closeProtectedDrawer"
      >
      </ProtectedDrawer>
    </template>
  </detail-page>
</template>

<script src="./ContractQuotationDetail.ts"></script>

<style lang="scss" scope>
// 悬停弹窗样式
.popper-class {
  max-width: 500px;
}

.detail {
  padding: 12px;

  &-main {
    .bottom-margin {
      margin-bottom: 16px;
    }

    .detail-btn {
      font-size: 12px;
      color: #3b71fc;
      line-height: 18px;
      cursor: pointer;
    }

    /deep/ .el-tabs__header {
      margin-bottom: 8px;
    }

    .third-tab {
      &-title {
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }

      &-tip {
        height: 44px;
        background: #fff5e6;
        border-radius: 4px;
        margin-bottom: 8px;

        &-item {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        span {
          font-size: 14px;
          font-weight: bold;
          margin: 0px 8px;
        }
      }
    }

    .textLength {
      white-space: normal;
      word-break: break-all;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 4; // 代表文本超长最多显示2行，可自行调整最多显示的行数
      -webkit-box-orient: vertical;
    }

    .icon-info {
      margin-top: 4px;
      color: #a1b0c8;
      font-size: 14px;
      margin: 0px 4px;

      &:hover {
        color: $--color-primary;
      }
    }
  }

  .tab-step2-toolbar-text {
    // display: flex;
    // justify-content: space-between;
    // align-items: center;
    // margin-bottom: 8px;

    // &-text {
    //   font-size: 18px;
    //   font-weight: 600;
    // }
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
  }
}

/deep/ .el-table__header-wrapper .el-table__cell {
  // 以下为UI样式
  padding-top: 6px;
  padding-bottom: 8px;
}

/deep/ .el-table__fixed-header-wrapper .el-table__cell {
  // 以下为UI样式
  padding-top: 6px;
  padding-bottom: 8px;
}

/deep/ .el-table__fixed-body-wrapper .el-table__cell {
  // 以下为UI样式
  padding-top: 6px;
  padding-bottom: 8px;
}

/deep/ .el-table__body-wrapper .el-table__cell {
  // 以下为UI样式
  padding-top: 6px;
  padding-bottom: 8px;
}

.el-table__fixed {
  height: 100%;
}

.el-table__fixed-right {
  height: 100%;
}
</style>
