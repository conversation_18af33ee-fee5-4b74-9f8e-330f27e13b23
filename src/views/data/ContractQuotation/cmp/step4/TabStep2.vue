<!--
 * @Author: 张文轩
 * @Date: 2024-06-27 17:22:03
 * @LastEditTime: 2024-09-11 10:53:04
 * @LastEditors: 张文轩
 * @Description:
 * @FilePath: \ltc-web-ui\src\views\data\ContractQuotation\cmp\step4\TabStep2.vue
 * 记得注释
-->
<template>
  <div class="tab-step2" ref="step2" :style="{ '--height': height }">
    <div class="tab-step2-toolbar">
      <div class="tab-step2-toolbar-text" style="display: flex;">
        <div style="margin-right: 4px;">报价总金额:</div>
        <div>{{ baseEntity.currencySymbol }}</div>
        <div>{{ totalAmount | numberFilter('--', 0, true) }}</div>
      </div>
      <div>
        <el-button v-if="showApplyBtn" type="text" @click="applySpecRate">
          申请特殊信保费率
        </el-button>
      </div>
    </div>
    <div class="tab-step2-table">
      <el-table ref="table" :data="entity.lines" v-loading="loading">
        <el-table-column label="付款方式" minWidth="280">
          <template #default="scoped">
            <el-cascader
              size="mini"
              style="width: 100%;"
              v-model="scoped.row.paymentTypeId"
              :ref="'cascader' + scoped.$index"
              :show-all-levels="false"
              :options="paymentTypeList"
              :props="cascaderProps"
              :disabled="otherDisabled"
              @change="
                () => {
                  paymentTypeChange(scoped.row, scoped.$index);
                }
              "
            ></el-cascader>
            <div style="position: absolute; bottom: 0px;">{{ scoped.row.paymentTypeName }}</div>
          </template>
        </el-table-column>
        <el-table-column label="付款比例" minWidth="104">
          <template #default="scoped">
            <number-input
              size="mini"
              v-model="scoped.row.paymentRatio"
              xType="float"
              :min="0.01"
              :max="getRatioMax(scoped.$index)"
              :disabled="otherDisabled"
            >
              <template slot="append">
                <div style="width: 26px; height: 26px; line-height: 26px;">%</div>
              </template>
            </number-input>
          </template>
        </el-table-column>
        <el-table-column label="付款金额" minWidth="160">
          <template #default="scoped">
            <div style="display: flex;">
              <div>
                {{ baseEntity.currencySymbol }}
              </div>
              <div v-if="scoped.row.paymentRatio">
                {{ getAmount(scoped.$index) | numberFilter('--', 2, true) }}
              </div>
              <div v-else>
                --
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="天数" minWidth="80">
          <template #default="scoped">
            <number-input
              v-if="scoped.row.payForward"
              size="mini"
              v-model="scoped.row.days"
              xType="num"
              :disabled="otherDisabled"
              @change="daysChange(scoped.row, scoped.$index)"
            >
            </number-input>
            <div v-else>--</div>
          </template>
        </el-table-column>
        <el-table-column label="远期贴现费率（年利率）" minWidth="124">
          <template #default="scoped">
            <div v-if="scoped.row.payForward && scoped.row.calForwardRate === true">
              <div v-if="!ObjectUtil.isNullOrBlank(scoped.row.forwardDiscountRate)">
                {{
                  PrecisionUtil.floatMul(scoped.row.forwardDiscountRate, 100)
                    | numberFilter('--', 2, true)
                }}%
              </div>
              <div v-else style="color: #de3232;">
                未获取到
              </div>
            </div>
            <div v-else>--</div>
          </template>
        </el-table-column>
        <el-table-column label="信保费率" minWidth="104">
          <template #default="scoped">
            <div v-if="scoped.row.payForward && scoped.row.calCreditInsurance === true">
              <div v-if="!ObjectUtil.isNullOrBlank(scoped.row.creditInsuranceRate)">
                {{
                  PrecisionUtil.floatMul(scoped.row.creditInsuranceRate, 100)
                    | numberFilter('--', 4, true)
                }}%
              </div>
              <div v-else style="color: #de3232;">
                未获取到
              </div>
            </div>
            <div v-else>--</div>
          </template>
        </el-table-column>
        <el-table-column v-if="!otherDisabled" label="操作" prop="操作" minWidth="80">
          <template #default="scoped">
            <el-button type="text" @click="doDelete(scoped.$index)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-button
        v-if="showAddBtn"
        class="table-btn"
        icon="el-icon-plus"
        type="primary"
        size="mini"
        @click="doAdd"
      >
        添加
      </el-button>
    </div>
  </div>
</template>

<script src="./TabStep2.ts"></script>

<style lang="scss" scoped>
.tab-step2 {
  min-height: 282px;
  height: var(--height);
  overflow-y: auto;
  &-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    &-text {
      font-size: 18px;
      font-weight: 600;
    }
  }
  &-table {
    height: calc(100% - 40px);
    .table-btn {
      color: #3b71fc;
      background: #ffffff;
      border-radius: 2px;
      border: 1px solid #3b71fc;
      margin-top: 12px;
    }
  }
}
.el-radio {
  margin-right: 8px;
}
/deep/ .el-table__header-wrapper .el-table__cell {
  // 以下为UI样式
  padding-top: 6px;
  padding-bottom: 8px;
}
/deep/ .el-table__fixed-header-wrapper .el-table__cell {
  // 以下为UI样式
  padding-top: 6px;
  padding-bottom: 8px;
}
/deep/ .el-table__body-wrapper .el-table__cell {
  // 以下为UI样式
  padding-top: 16px;
  padding-bottom: 16px;
}
/deep/ .el-table__fixed-body-wrapper .el-table__cell {
  // 以下为UI样式
  padding-top: 16px;
  padding-bottom: 16px;
}
</style>
