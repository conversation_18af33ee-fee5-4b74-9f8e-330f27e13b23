<template>
  <el-dialog title="操作日志" width="1200px" @close="handleCancel" :visible="true">
    <el-form size="small" :model="filter" label-width="80px">
      <el-row>
        <el-col :span="6">
          <el-form-item label="操作类型" prop="actionEquals">
            <el-select v-model="filter.actionEquals" placeholder="请选择" style="width: 100%;">
              <el-option
                v-for="item in actionList"
                :label="item.label"
                :value="item.value"
                :key="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="操作人" prop="operatorIdEquals">
            <search-remote
              v-model="filter.operatorIdEquals"
              placeholder="输入搜索"
              valueKey="custId"
              :queryMethod="queryCreateList"
              :valueFormat="createValueFormat"
              :labelFormat="createLabelFormat"
              :defaultValue="[
                { code: 'CPQ', name: 'CPQ' },
                { code: '系统操作', name: '系统操作' },
              ]"
              :multiple="false"
              :lazyLoad="true"
            ></search-remote>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="操作时间" prop="operateTime">
            <el-date-picker
              v-model="filter.operateTime"
              type="daterange"
              range-separator="~"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd HH:mm:ss"
              format="yyyy-MM-dd"
              :default-time="['00:00:00', '23:59:59']"
              :picker-options="pickerOptions"
              @change="operateTimeChange"
              style="width: 100%;"
              :clearable="false"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label=" ">
            <el-button size="mini" type="primary" @click="handleSearch">查询</el-button>
            <el-button size="mini" @click="handleReset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="tip-info">{{ tipLabel }}: {{ objectPid }}</div>
    <el-table v-loading="loading" style="margin-top: 4px" max-height="400" :data="tableData" border>
      <el-table-column label="操作类型">
        <template #default="scope">{{ scope.row.action }}</template>
      </el-table-column>
      <el-table-column label="操作描述">
        <template #default="scope">{{ scope.row.remark }}</template>
      </el-table-column>
      <el-table-column label="操作变更的值">
        <template #default="scope">
          <div
            style="color: #3b71fc; cursor: pointer;"
            v-if="scope.row.changeFields && scope.row.changeFields.length > 0"
            @click="doView(scope.row.changeFields)"
          >
            查看
          </div>
          <div v-else>--</div>
        </template>
      </el-table-column>
      <el-table-column label="操作结果">
        <template #default="scope">{{ scope.row.operateResult }}</template>
      </el-table-column>
      <el-table-column label="操作时间">
        <template #default="scope">{{ scope.row.operateTime }}</template>
      </el-table-column>
      <el-table-column label="操作人">
        <template #default="scope">
          {{ scope.row.operatorName }}
          {{ scope.row.operatorId ? '[' + scope.row.operatorId + ']' : '' }}
        </template>
      </el-table-column>
      <el-table-column label="操作IP">
        <template #default="scope">{{ scope.row.ip }}</template>
      </el-table-column>
    </el-table>
    <div style="display: flex; justify-content: flex-end; margin-top: 10px;">
      <el-button-group style="margin-right: 16px;">
        <el-button
          size="small"
          @click="changePage(filter.page - 1, filter.pageSize)"
          :disabled="filter.page === 0"
        >
          <i class="el-icon-arrow-left el-icon--left"></i>
          上一页
        </el-button>
        <el-button
          size="small"
          @click="changePage(filter.page + 1, filter.pageSize)"
          :disabled="filter.pageSize !== tableData.length"
        >
          下一页
          <i class="el-icon-arrow-right el-icon--right"></i>
        </el-button>
      </el-button-group>
      <el-select
        size="small"
        v-model="filter.pageSize"
        style="width: 100px;"
        placeholder="请选择"
        @change="changePage(0, filter.pageSize)"
      >
        <el-option label="10条/页" :value="10"> </el-option>
        <el-option label="20条/页" :value="20"> </el-option>
        <el-option label="50条/页" :value="50"> </el-option>
        <el-option label="100条/页" :value="100"> </el-option>
      </el-select>
    </div>
  </el-dialog>
</template>
<script lang="ts" src="./HdOperationLog.ts"></script>
<style lang="scss" scoped>
::v-deep .el-dialog {
  margin-top: 0px !important;
}
.tip-info {
  display: flex;
  width: 300px;
  background-color: #f1f1f5;
  padding: 6px 8px;
  margin-bottom: 10px;
}
</style>
