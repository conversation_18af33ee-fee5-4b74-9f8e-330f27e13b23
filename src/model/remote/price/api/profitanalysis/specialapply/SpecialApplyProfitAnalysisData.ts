import SpecialApplyFee from 'model/remote/price/api/specialapply/otherexpenses/SpecialApplyFee'

// 特价申请毛利分析-数据模型
export default class SpecialApplyProfitAnalysisData {
  // 额外费用用途
  extraFeeUsage: Nullable<string> = null
  // 产品线毛利额
  productLineProfit: Nullable<SpecialApplyFee> = null
  // 子公司毛利额
  subsidiaryProfit: Nullable<SpecialApplyFee> = null
  // 国际部毛利额
  internationalDepartmentProfit: Nullable<SpecialApplyFee> = null
  // 生产事业部毛利额
  productionProfit: Nullable<SpecialApplyFee> = null
  // 产品线毛利率
  productLineRate: Nullable<number> = null
  // 子公司毛利率
  subsidiaryRate: Nullable<number> = null
  // 国际部毛利率
  internationalDepartmentRate: Nullable<number> = null
  // 生产事业部毛利率
  productionRate: Nullable<number> = null
}