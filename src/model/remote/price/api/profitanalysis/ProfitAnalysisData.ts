import SpecialApplyFee from 'model/remote/price/api/specialapply/otherexpenses/SpecialApplyFee'

// 毛利分析-数据模型
export default class ProfitAnalysisData {
  // 远期贴现费
  forwardDiscountFee: Nullable<SpecialApplyFee> = null
  // 信保费
  premiumFee: Nullable<SpecialApplyFee> = null
  // 国际段运杂费
  internationalTransportationFee: Nullable<SpecialApplyFee> = null
  // 国际运输保险费
  internationalTransportationInsuranceFee: Nullable<SpecialApplyFee> = null
  // 赠送配件金额
  giftAccessoryFee: Nullable<SpecialApplyFee> = null
  // 延长保修费
  extendedFee: Nullable<SpecialApplyFee> = null
  // 客户佣金
  commissionFee: Nullable<SpecialApplyFee> = null
  // 额外费用
  otherFee: Nullable<SpecialApplyFee> = null
  // 中国国内运费
  domesticTransportationFee: Nullable<SpecialApplyFee> = null
  // 中国国内运费(含税)
  domesticTransportationFeeOrigin: Nullable<SpecialApplyFee> = null
  // 国内运输保险费
  domesticTransportationInsuranceFee: Nullable<SpecialApplyFee> = null
  // 国内运输保险费(含税)
  domesticTransportationInsuranceFeeOrigin: Nullable<SpecialApplyFee> = null
  // 港杂/口岸费
  portFee: Nullable<SpecialApplyFee> = null
  // 港杂/口岸费(含税)
  portFeeOrigin: Nullable<SpecialApplyFee> = null
  // 装箱费
  packingFee: Nullable<SpecialApplyFee> = null
  // 装箱费(含税)
  packingFeeOrigin: Nullable<SpecialApplyFee> = null
  // 包装防护费
  packagingProtectionFee: Nullable<SpecialApplyFee> = null
  // 包装防护费(含税)
  packagingProtectionFeeOrigin: Nullable<SpecialApplyFee> = null
  // 整机生产成本
  productionFee: Nullable<SpecialApplyFee> = null
  // 生产事业部出厂价
  factoryPrice: Nullable<SpecialApplyFee> = null
  // 产品线利润
  productLineProfit: Nullable<SpecialApplyFee> = null
  // 子公司利润
  subsidiaryProfit: Nullable<SpecialApplyFee> = null
  // 国际部利润
  internationalDepartmentProfit: Nullable<SpecialApplyFee> = null
  // 生产事业部利润
  productionProfit: Nullable<SpecialApplyFee> = null
  // 产品线利率
  productLineRate: Nullable<number> = null
  // 子公司利率
  subsidiaryRate: Nullable<number> = null
  // 国际部利率
  internationalDepartmentRate: Nullable<number> = null
  // 生产事业部利率
  productionRate: Nullable<number> = null
}