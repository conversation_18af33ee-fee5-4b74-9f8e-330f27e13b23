import ApplicationScope from 'model/remote/price/api/specialapply/bill/ApplicationScope'

// 特价申请单物料区域指导价
export default class SpecialApplyMatLineScopeExwPriceOverview {
  // ID
  id: Nullable<string> = null
  // 应用区域
  applicationScope: Nullable<ApplicationScope> = null
  // 是否限制数量
  limitQty: Nullable<boolean> = null
  // 限量台量
  qty: Nullable<number> = null
  // EXW指导价
  exwPrice: Nullable<number> = null
  // 调价幅度
  adjustRange: Nullable<number> = null
  // 调价差值
  differAmt: Nullable<number> = null
  // EXW设备特价
  specialPrice: Nullable<number> = null
  // 预估其它费用
  otherExpenses: Nullable<number> = null
  // 额外费用用途
  extraFeeUsage: Nullable<string> = null
  // 预估物流费用
  logisticsExpenses: Nullable<number> = null
  // 预估综合费用
  total: Nullable<number> = null
  // 价格有效开始日期yyyy-MM-dd，service模型，date
  beginDate: Nullable<string> = null
  // 价格有效结束日期yyyy-MM-dd，service模型，date
  endDate: Nullable<string> = null
}