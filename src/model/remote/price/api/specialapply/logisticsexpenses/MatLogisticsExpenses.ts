import SpecialApplyFee from 'model/remote/price/api/specialapply/otherexpenses/SpecialApplyFee'
import { SpecialApplyRuleEmptyType } from 'model/remote/price/api/specialapply/logisticsexpenses/SpecialApplyRuleEmptyType'

// 物料物流费用
export default class MatLogisticsExpenses {
  // 匹配规则id。供货的装箱费、运输保险费等费用没有规则
  ruleId: Nullable<string> = null
  // 匹配规则缺失类型，固定枚举："装运方案缺失"、"规则缺失"、"物料体积缺失"。可为空。匹配规则id为空时返回。供货的装箱费、运输保险费等费用没有匹配规则缺失说明
  ruleEmptyType: Nullable<SpecialApplyRuleEmptyType> = null
  // 原币的币种，金额。供货的装箱费费用没有原币
  originFee: Nullable<SpecialApplyFee> = null
  // 币种，金额
  fees: SpecialApplyFee[] = []
}