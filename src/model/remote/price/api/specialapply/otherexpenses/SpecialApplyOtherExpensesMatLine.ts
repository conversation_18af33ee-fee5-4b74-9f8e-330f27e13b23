import ApplicationScopeExtended from 'model/remote/price/api/specialapply/otherexpenses/ApplicationScopeExtended'
import MatOtherExpenses from 'model/remote/price/api/specialapply/otherexpenses/MatOtherExpenses'
import SpecialApplyCurrency from 'model/remote/price/api/specialapply/otherexpenses/SpecialApplyCurrency'
import SpecialApplyMatLine from 'model/remote/price/api/specialapply/mat/SpecialApplyMatLine'

// 设备特价申请单其他费用信息物料信息明细
export default class SpecialApplyOtherExpensesMatLine {
  // ID
  id: Nullable<string> = null
  // 所属特价申请单, SpecialApplyBill.id
  owner: Nullable<string> = null
  //  所属设备特价申请单物料明细, SpecialApplyMatLine.id
  ownerMat: Nullable<string> = null
  // 物料明细
  matLine: Nullable<SpecialApplyMatLine> = null
  // 区域延保费
  scopeExtendeds: ApplicationScopeExtended[] = []
  // 币种清单。用于前端展示枚举
  currencyIds: SpecialApplyCurrency[] = []
  // 客户佣金/台
  commission: Nullable<MatOtherExpenses> = null
  // 赠送配件金额/台
  giftAccessoryAmount: Nullable<MatOtherExpenses> = null
  // 额外费用/台
  otherExpenses: Nullable<MatOtherExpenses> = null
  // 额外费用用途。非必填。当额外费用不为空时必填
  extraFeeUsage: Nullable<string> = null
}