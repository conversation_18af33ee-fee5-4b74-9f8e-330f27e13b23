import ApplicationScopeExpenseRateAmount from 'model/remote/price/api/specialapply/otherexpenses/ApplicationScopeExpenseRateAmount'
import ApplicationScopeExtended from 'model/remote/price/api/specialapply/otherexpenses/ApplicationScopeExtended'
import SpecialApplyCurrency from 'model/remote/price/api/specialapply/otherexpenses/SpecialApplyCurrency'
import SpecialApplyFee from 'model/remote/price/api/specialapply/otherexpenses/SpecialApplyFee'
import SpecialApplyMatLine from 'model/remote/price/api/specialapply/mat/SpecialApplyMatLine'

// 设备特价申请单其他费用汇总
export default class SpecialApplyOtherExpensesSummary {
  // ID
  id: Nullable<string> = null
  // 排序
  line: Nullable<number> = null
  // 所属特价申请单 , SpecialApplyBill.id
  owner: Nullable<string> = null
  // 所属物料id , SpecialApplyMatLine.id; 前端不需关注
  ownerMat: Nullable<string> = null
  // 物料明细
  specialApplyMatLine: Nullable<SpecialApplyMatLine> = null
  // 币种清单。不为空，费用为空时，用于前端展示币种枚举
  currencyIds: SpecialApplyCurrency[] = []
  // 延保政策明细json
  extendedInfos: ApplicationScopeExtended[] = []
  // 佣金json
  commission: SpecialApplyFee[] = []
  // 赠送配件金额
  giftAccessoryAmount: SpecialApplyFee[] = []
  // 额外费用
  other: SpecialApplyFee[] = []
  // 额外费用用途
  extraFeeUsage: Nullable<string> = null
  // 远期贴现费
  forwardDiscount: ApplicationScopeExpenseRateAmount[] = []
  // 信保费
  premium: ApplicationScopeExpenseRateAmount[] = []
  // 国内运输保险费
  domesticTransportationInsurance: ApplicationScopeExpenseRateAmount[] = []
  // 国际端运输保险费
  internationalTransportationPremium: ApplicationScopeExpenseRateAmount[] = []
}