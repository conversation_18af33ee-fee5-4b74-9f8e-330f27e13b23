import ApplicationScope from 'model/remote/price/api/specialapply/bill/ApplicationScope'
import LtcEntityVo from 'model/remote/support/core/domain/LtcEntityVo'
import UserTaskOutGoing from 'model/remote/price/api/quotationapply/bill/bpfm/UserTaskOutGoing'
import { ExportType } from 'model/remote/price/model/po/subsidiarycommissionrate/ExportType'
import { GuidePriceType } from 'model/remote/price/model/po/GuidePriceType'
import { MatApplyType } from 'model/remote/price/api/specialapply/bill/MatApplyType'
import { SpecialApplyState } from 'model/remote/price/api/specialapply/bill/SpecialApplyState'
import { SpecialType } from 'model/remote/price/api/specialapply/bill/SpecialType'

// 特价申请单
export default class SpecialApplyBill extends LtcEntityVo {
  // 存储单号
  id: Nullable<string> = null
  // 所属组织id
  orgId: Nullable<string> = null
  // 主题
  topic: Nullable<string> = null
  // 是否加急审批
  urgentApprove: Nullable<boolean> = null
  // 报价是否包含以下费用项:物流费用、延保费、客户佣金、赠送配件金额、额外费用、远期贴现费、信保费、国内运输保险费、国际运输保险费
  otherExpenses: Nullable<boolean> = null
  // 是否允许折扣
  discount: Nullable<boolean> = null
  // 特价类型
  specialType: Nullable<SpecialType> = null
  //  出口类型，来自数据字典
  exportTypeId: Nullable<ExportType> = null
  // 销售模式
  saleMode: Nullable<GuidePriceType> = null
  // 贸易术语，来自数据字典。如果没有值，则固定EXW。
  incotermsId: Nullable<string> = null
  // 补充价格币种id ，来自数据字典
  supplementPriceCcyId: Nullable<string> = null
  // 补充价格币种名称，来自数据字典
  supplementPriceCcyName: Nullable<string> = null
  // 补充价格币种符号，来自数据字典
  supplementPriceCcySymbol: Nullable<string> = null
  // 价格有效开始日期yyyy-MM-dd，service模型，date
  beginDate: Nullable<string> = null
  // 价格有效结束日期yyyy-MM-dd，service模型，date
  endDate: Nullable<string> = null
  // 特价适用范围
  applicationScope: ApplicationScope[] = []
  // 状态
  state: Nullable<SpecialApplyState> = null
  // 审批流程ID
  approveProcessId: Nullable<string> = null
  // 版本号
  version: Nullable<number> = null
  // 任务出口。
  userTaskOutGoings: UserTaskOutGoing[] = []
  // 任务节点状态名称。
  taskState: Nullable<string> = null
  // 任务节点状态代码。
  taskStateCode: Nullable<string> = null
  // 物料申请类型
  applyType: Nullable<MatApplyType> = null
  // 自营-国际运输模式，来自数据字典
  transportTypeId: Nullable<string> = null
  // 自营-国际运输模式类型-滚装、散杂，来自数据字典
  transportTypeRoleId: Nullable<string> = null
  // 自营-目的港目的站 代码
  destinationPortCode: Nullable<string> = null
  // 自营-目的港目的站 名称
  destinationPortName: Nullable<string> = null
  // 供货-交货地点 代码
  domesticFreightLandCode: Nullable<string> = null
  // 供货-交货地点 名称
  domesticFreightLandName: Nullable<string> = null
  // 供货-国内运输模式。car-汽车
  dmTransportTypeId: Nullable<string> = null
  // 国内运输保险费是否投保
  domesticTransInsurancePremium: Nullable<boolean> = null
  // 上次汇率更新时间
  rateRefreshTime: Nullable<Date> = null
}