import SpecialApplyMatLineScopeExwPriceOverview from 'model/remote/price/api/specialapply/mat/SpecialApplyMatLineScopeExwPriceOverview'

// 计算物料其它费用请求
export default class CalMatLineOtherFee {
  //  特价单id
  owner: Nullable<string> = null
  // 是否包含以下费用项:物流费用、延保费、客户佣金、赠送配件金额、额外费用、远期贴现费、信保费、国内运输保险费、国际运输保险费
  otherExpenses: boolean = false
  //  物料id
  matId: Nullable<string> = null
  //  行号
  line: Nullable<number> = null
  //  物料号
  matCd: Nullable<string> = null
  // 国际产品组id
  i18ProdGroupId: Nullable<string> = null
  // 国际产品线名称
  i18ProdGroupName: Nullable<string> = null
  // 机型ID
  prodMdlId: Nullable<string> = null
  // 机型代码
  prodMdlCode: Nullable<string> = null
  // 物料描述
  matDesc: Nullable<string> = null
  // 物料描述(英文)
  matDescEn: Nullable<string> = null
  //  是否限制数量
  limitQty: Nullable<boolean> = null
  //  限量台量
  qty: Nullable<number> = null
  // 特价申请单物料区域指导价
  exwPrice: Nullable<SpecialApplyMatLineScopeExwPriceOverview> = null
}